# 精准查涉案API接口

基于Apifox文档实现的精准查涉案API接口封装，支持个人和企业涉案信息查询。

## 目录结构

```
extend/api/
├── GoodCheckApi.php          # 主要API类
├── ApiFactory.php            # API工厂类
├── config/
│   └── ApiConfig.php         # 配置管理类
├── example/
│   └── usage_example.php     # 使用示例
└── README.md                 # 说明文档
```

## 功能特性

- ✅ 个人涉案信息查询
- ✅ 企业涉案信息查询
- ✅ 自动签名生成（MD5加密）
- ✅ 完整的错误处理
- ✅ 支持自定义参数
- ✅ 工厂模式简化调用
- ✅ 配置文件管理

## 快速开始

### 1. 配置API密钥

编辑 `config/ApiConfig.php` 文件，设置你的API密钥：

```php
const GOOD_CHECK_API = [
    'app_id' => 'your_app_id',        // 替换为实际的appid
    'secret' => 'your_secret_key',    // 替换为实际的密钥
    'url' => 'https://api.k-wz.com/api/goodcheckapi',
];
```

### 2. 基本使用

#### 个人涉案查询

```php
use api\ApiFactory;

// 快速查询
$result = ApiFactory::quickCheckPerson('张三', '420101199001010001');

if ($result['success']) {
    echo "查询成功";
    print_r($result['data']);
} else {
    echo "查询失败：" . $result['message'];
}
```

#### 企业涉案查询

```php
use api\ApiFactory;

// 快速查询
$result = ApiFactory::quickCheckCompany('某某科技有限公司', '91110000123456789X');

if ($result['success']) {
    echo "查询成功";
    print_r($result['data']);
} else {
    echo "查询失败：" . $result['message'];
}
```

### 3. 高级使用

#### 使用完整参数

```php
use api\GoodCheckApi;

$api = new GoodCheckApi('your_app_id', 'your_secret');

$params = [
    'orderno' => 'ORDER_' . time(),
    'name' => ['张三', '李四'],  // 支持多个姓名
    'code' => '420101199001010001',
    'detail' => '1',           // 1为详细版，0为简版
    'client_ip' => '***********',
    'interfacecoding' => 'custom_001'
];

$result = $api->goodCheck($params);
```

## API参数说明

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderno | string | 否 | 订单号 |
| nonce_str | string | 否 | 32位随机字符串（自动生成） |
| name | array | 否 | 查询个人名称或企业名称 |
| code | string | 否 | 完整身份证号或企业税号 |
| datanumber | string | 否 | 固定填写1 |
| detail | string | 否 | 0简版，1详细版 |
| serviceCode | string | 否 | 固定ALL |
| classtype | string | 否 | 接口类型，固定sf |
| checktype | string | 否 | 1查询，TB备付金同步 |
| interfacecoding | string | 否 | 自定义查询编码 |
| querytype | string | 否 | 查询类型，0个人，1企业 |
| client_ip | string | 否 | 查询IP |

### 返回格式

```php
[
    'success' => true/false,    // 请求是否成功
    'message' => '消息内容',     // 成功或错误消息
    'data' => []               // 返回的数据或原始响应
]
```

## 在ThinkPHP中使用

### 控制器示例

```php
<?php
namespace app\index\controller;

use think\Controller;
use api\ApiFactory;

class CaseCheck extends Controller
{
    /**
     * 个人涉案查询
     */
    public function checkPerson()
    {
        $name = $this->request->param('name');
        $idCard = $this->request->param('id_card');
        
        if (empty($name) || empty($idCard)) {
            return json(['code' => 400, 'msg' => '参数不能为空']);
        }
        
        $options = [
            'orderno' => 'ORDER_' . time(),
            'client_ip' => $this->request->ip(),
            'interfacecoding' => 'web_query_001'
        ];
        
        $result = ApiFactory::quickCheckPerson($name, $idCard, $options);
        
        if ($result['success']) {
            return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
        } else {
            return json(['code' => 500, 'msg' => $result['message']]);
        }
    }
    
    /**
     * 企业涉案查询
     */
    public function checkCompany()
    {
        $companyName = $this->request->param('company_name');
        $taxNumber = $this->request->param('tax_number');
        
        if (empty($companyName) || empty($taxNumber)) {
            return json(['code' => 400, 'msg' => '参数不能为空']);
        }
        
        $options = [
            'orderno' => 'COMPANY_ORDER_' . time(),
            'client_ip' => $this->request->ip()
        ];
        
        $result = ApiFactory::quickCheckCompany($companyName, $taxNumber, $options);
        
        if ($result['success']) {
            return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
        } else {
            return json(['code' => 500, 'msg' => $result['message']]);
        }
    }
}
```

## 签名算法

API使用MD5签名验证，签名生成步骤：

1. 将所有非空参数按参数名ASCII码从小到大排序
2. 使用URL键值对格式拼接成字符串stringA
3. 在stringA最后拼接timestamp和secret
4. 对最终字符串进行MD5运算得到签名

## 注意事项

1. 请确保API密钥的安全性，不要在前端暴露
2. 建议在生产环境中使用HTTPS
3. 合理设置请求超时时间
4. 做好错误处理和日志记录
5. 遵守API调用频率限制

## 错误处理

API会返回统一的错误格式，包含以下类型的错误：

- CURL错误：网络连接问题
- HTTP错误：服务器响应错误
- JSON解析错误：响应格式问题
- 业务逻辑错误：API返回的业务错误

## 更新日志

- v1.0.0 (2025-06-22)
  - 初始版本发布
  - 支持个人和企业涉案查询
  - 完整的签名验证机制
  - 工厂模式封装
