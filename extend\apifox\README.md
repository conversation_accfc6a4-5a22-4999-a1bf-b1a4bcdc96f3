# 聘查查API接口

基于Apifox文档实现的聘查查API接口封装，支持多种个人和企业信息查询服务。

## 目录结构

```
extend/apifox/
├── PinccApi.php              # 主要API类
├── ApifoxFactory.php         # API工厂类
├── config/
│   └── ApifoxConfig.php      # 配置管理类
├── example/
│   └── usage_example.php     # 使用示例
└── README.md                 # 说明文档
```

## 功能特性

- ✅ 全国婚姻查询
- ✅ 学历查询（星耀版/至尊版）
- ✅ 失信模型查询（个人/企业）
- ✅ 个人名下车辆查询
- ✅ 人企任职记录查询
- ✅ 个人综合资质评分
- ✅ 完整的错误处理
- ✅ 工厂模式简化调用
- ✅ 配置文件管理

## 快速开始

### 1. 配置API密钥

编辑 `config/ApifoxConfig.php` 文件，设置你的API密钥：

```php
const PINCC_API = [
    'app_id' => 'your_app_id',        // 替换为实际的appid
    'secret' => 'your_secret_key',    // 替换为实际的密钥
    'secret_key' => 'your_secret_key', // 兼容旧版本
    'base_url' => 'https://api.edazi.com/api',
    'timeout' => 30,                  // 请求超时时间（秒）
    'debug' => false,                 // 是否开启调试模式
];
```

**重要提示**：只需要修改 `app_id` 和 `secret` 这两个配置项，其他配置保持默认即可。

#### 环境变量配置（推荐）

为了安全起见，建议使用环境变量配置敏感信息：

```bash
# 在 .env 文件中设置
PINCC_APP_ID=your_actual_app_id
PINCC_SECRET=your_actual_secret_key
PINCC_DEBUG=false
```

### 2. 基本使用

#### 婚姻状况查询

```php
use apifox\ApifoxFactory;

// 快速查询
$result = ApifoxFactory::quickMarriageQuery('张三', '******************');

if ($result['success']) {
    $data = $result['data'];
    echo "婚姻状况：" . $data['message']; // 未婚/已婚/离异/婚姻冷静期
    echo "状态码：" . $data['status'];   // 0-未婚 1-已婚 2-离异 3-婚姻冷静期
} else {
    echo "查询失败：" . $result['message'];
}
```

#### 学历查询

```php
use apifox\ApifoxFactory;

// 星耀版学历查询
$result = ApifoxFactory::quickEducationQueryV6('李四', '******************');

// 至尊版学历查询（限量）
$result = ApifoxFactory::quickEducationQueryV4('王五', '******************');

if ($result['success']) {
    $data = $result['data'];
    if ($data['resultCode'] == 1) {
        echo "查询成功有结果";
        print_r($data['detail']); // 学历详情
    } else {
        echo "查询成功无结果";
    }
}
```

#### 失信查询

```php
use apifox\ApifoxFactory;

// 个人失信查询
$result = ApifoxFactory::quickPersonCreditQuery('张三', '******************');

// 企业失信查询
$result = ApifoxFactory::quickCompanyCreditQuery('某某科技有限公司', '91110000123456789X');

if ($result['success']) {
    $data = $result['data'];
    if ($data['resultCode'] == 1) {
        echo "发现失信记录";
        print_r($data['detail']);
    } else {
        echo "无失信记录";
    }
}
```

### 3. 高级使用

#### 车辆查询

```php
use apifox\PinccApi;

$api = new PinccApi('your_secret_key');

// 查询个人名下车辆
$result = $api->personalVehicle(
    '******************',  // 身份证号
    '2',                   // 关系类型：1-ETC开户人；2-车辆所有人；3-ETC经办人
    '张三',                // 姓名（可选）
    2                      // 车辆类型：0-客车；1-货车；2-全部
);
```

#### 综合资质评分

```php
use apifox\ApifoxFactory;

$result = ApifoxFactory::quickQualifyScoreQuery(
    '张三',                // 姓名
    '******************',  // 身份证号
    '13800138000'          // 手机号
);

if ($result['success']) {
    $data = $result['data'];
    echo "资质评分：" . $data['amount'];
}
```

## API接口说明

### 1. 全国婚姻查询

- **接口**：`/Pincc/marriage`
- **参数**：姓名、身份证号
- **返回**：婚姻状态（未婚/已婚/离异/婚姻冷静期）

### 2. 学历查询

- **星耀版**：`/JH/verifyV6`
- **至尊版**：`/JH/verifyV4`
- **参数**：姓名、身份证号
- **返回**：学历详情（院校、专业、学历层次、毕业日期等）

### 3. 失信模型查询

- **接口**：`/JH/creditDishonestyV3`
- **参数**：姓名/企业名称、身份证号/统一社会信用代码、实体类型
- **返回**：失信记录详情

### 4. 个人名下车辆

- **接口**：`/JH/personalVehicle`
- **参数**：身份证号、关系类型、姓名（可选）、车辆类型（可选）
- **返回**：车辆信息列表

### 5. 人企任职记录

- **接口**：`/JH/creditEnterpriseV3`
- **参数**：身份证号
- **返回**：任职企业记录

### 6. 个人综合资质评分

- **接口**：`/JH/qualifyScore`
- **参数**：姓名、身份证号、手机号
- **返回**：综合资质评分

## 在ThinkPHP中使用

### 控制器示例

```php
<?php
namespace app\index\controller;

use think\Controller;
use apifox\ApifoxFactory;

class PersonCheck extends Controller
{
    /**
     * 婚姻状况查询
     */
    public function marriageCheck()
    {
        $name = $this->request->param('name');
        $cardNo = $this->request->param('card_no');
        
        if (empty($name) || empty($cardNo)) {
            return json(['code' => 400, 'msg' => '参数不能为空']);
        }
        
        $result = ApifoxFactory::quickMarriageQuery($name, $cardNo);
        
        if ($result['success']) {
            return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
        } else {
            return json(['code' => 500, 'msg' => $result['message']]);
        }
    }
    
    /**
     * 学历查询
     */
    public function educationCheck()
    {
        $fullName = $this->request->param('full_name');
        $idCardNo = $this->request->param('id_card_no');
        $version = $this->request->param('version', 'v6'); // v6星耀版，v4至尊版
        
        if (empty($fullName) || empty($idCardNo)) {
            return json(['code' => 400, 'msg' => '参数不能为空']);
        }
        
        if ($version === 'v4') {
            $result = ApifoxFactory::quickEducationQueryV4($fullName, $idCardNo);
        } else {
            $result = ApifoxFactory::quickEducationQueryV6($fullName, $idCardNo);
        }
        
        if ($result['success']) {
            return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
        } else {
            return json(['code' => 500, 'msg' => $result['message']]);
        }
    }
}
```

## 返回格式

所有接口都返回统一的格式：

```php
[
    'success' => true/false,    // 请求是否成功
    'message' => '消息内容',     // 成功或错误消息
    'data' => []               // 返回的数据
]
```

API原始返回格式：

```php
[
    'code' => 200,             // 系统码，200为正确
    'msg' => '成功',           // 错误描述
    'data' => [
        'code' => null,
        'requestId' => 'xxx',   // 请求ID
        'messge' => '说明',     // 说明信息
        'resultCode' => 0,      // 结果代码：0-无结果；1-有结果
        'detail' => []          // 详细数据
    ]
]
```

## 注意事项

1. 请确保API密钥的安全性，不要在前端暴露
2. 建议在生产环境中使用HTTPS
3. 合理设置请求超时时间
4. 做好错误处理和日志记录
5. 遵守API调用频率限制
6. 部分接口为限量版本，请合理使用

## 更新日志

- v1.0.0 (2025-06-22)
  - 初始版本发布
  - 支持7个核心查询接口
  - 完整的错误处理机制
  - 工厂模式封装
