-- ----------------------------
-- Table structure for fa_package
-- ----------------------------
CREATE TABLE `fa_package` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '套餐名称',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '套餐描述',
  `type` enum('single','package') NOT NULL DEFAULT 'single' COMMENT '类型:single=单项,package=套餐',
  `current_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '当前价格',
  `original_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '原价',
  `tags` text COMMENT '标签(逗号分隔)',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `weigh` (`weigh`),
  KEY `status` (`status`),
  KEY `type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='套餐表';

-- ----------------------------
-- Records of fa_package
-- ----------------------------
INSERT INTO `fa_package` VALUES 
(1, '基础信息查询', '身份验证、手机号验证', 'single', 5.80, 8.80, '', 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '收入信息', '工资收入、其他收入来源', 'single', 8.80, 12.80, '', 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '车辆信息', '车辆登记、车辆价值评估', 'single', 6.80, 9.80, '', 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, '学历信息', '学历验证、学校信息', 'single', 4.80, 7.80, '', 70, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, '企业法人', '法人代表信息、企业注册信息', 'single', 12.80, 18.80, '', 60, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(6, '经营风险', '企业风险评估、经营状况', 'single', 15.80, 22.80, '', 50, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(7, '综合水平', '收入信息、车辆信息、学历信息', 'package', 16.80, 26.80, '收入信息,车辆信息,学历信息', 100, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(8, '企业信息', '企业法人、企业高管、经营风险、关联企业、学历信息', 'package', 45.80, 88.80, '企业法人,企业高管,经营风险,关联企业,学历信息', 90, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(9, '全套服务', '基础信息、收入信息、车辆信息、学历信息、企业信息、风险评估', 'package', 88.80, 158.80, '基础信息,收入信息,车辆信息,学历信息,企业信息,风险评估', 80, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
