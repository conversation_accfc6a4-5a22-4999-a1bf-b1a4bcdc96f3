<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>查询进度 - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <style>
        .status-container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }
        .status-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .task-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .task-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .progress-section {
            margin: 20px 0;
        }
        .progress-bar-container {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .progress-text {
            text-align: center;
            font-weight: bold;
            color: #333;
            margin: 10px 0;
        }
        .task-list {
            margin: 20px 0;
        }
        .task-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
        }
        .task-item.completed {
            border-color: #28a745;
            background: #f8fff9;
        }
        .task-item.failed {
            border-color: #dc3545;
            background: #fff8f8;
        }
        .task-item.processing {
            border-color: #007bff;
            background: #f0f8ff;
        }
        .task-status-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .status-completed {
            background: #28a745;
            color: white;
        }
        .status-failed {
            background: #dc3545;
            color: white;
        }
        .status-processing {
            background: #007bff;
            color: white;
            animation: pulse 1.5s infinite;
        }
        .status-pending {
            background: #6c757d;
            color: white;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .task-name {
            flex: 1;
            font-weight: bold;
            color: #333;
        }
        .task-time {
            font-size: 12px;
            color: #666;
        }
        .status-message {
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .status-processing-msg {
            background: #e3f2fd;
            color: #1976d2;
        }
        .status-completed-msg {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status-failed-msg {
            background: #ffebee;
            color: #c62828;
        }
        .action-buttons {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .btn {
            padding: 12px 24px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .refresh-info {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="status-container">
        <div class="status-header">
            <div class="status-title">⏳ 查询进度</div>
            <div id="statusSubtitle">正在处理您的查询请求...</div>
        </div>

        <!-- 任务信息 -->
        <div class="task-info">
            <div class="task-info-item">
                <span><strong>任务ID：</strong></span>
                <span id="taskId">-</span>
            </div>
            <div class="task-info-item">
                <span><strong>查询姓名：</strong></span>
                <span id="queryName">-</span>
            </div>
            <div class="task-info-item">
                <span><strong>提交时间：</strong></span>
                <span id="submitTime">-</span>
            </div>
            <div class="task-info-item">
                <span><strong>预计完成：</strong></span>
                <span id="estimatedTime">-</span>
            </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-section">
            <div class="progress-text" id="progressText">0%</div>
            <div class="progress-bar-container">
                <div class="progress-bar-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
                <span>已完成：<span id="completedCount">0</span></span>
                <span>总任务：<span id="totalCount">0</span></span>
            </div>
        </div>

        <!-- 状态消息 -->
        <div id="statusMessage" class="status-message status-processing-msg">
            🔄 正在处理查询任务，请稍候...
        </div>

        <!-- 任务列表 -->
        <div class="task-list">
            <h4>📋 任务详情</h4>
            <div id="taskList"></div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn btn-secondary" onclick="refreshStatus()">🔄 刷新状态</button>
            <a href="/index/index" class="btn btn-primary">🏠 返回首页</a>
        </div>

        <div class="refresh-info">
            页面每5秒自动刷新一次状态
        </div>
    </div>

    <script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>
    <script>
        let currentTaskId = '';
        let pollInterval = null;

        // 页面加载完成后处理
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL参数获取任务ID
            const urlParams = new URLSearchParams(window.location.search);
            currentTaskId = urlParams.get('task_id');
            
            if (currentTaskId) {
                document.getElementById('taskId').textContent = currentTaskId;
                startPolling();
            } else {
                showError('未找到任务ID');
            }
        });

        // 开始轮询状态
        function startPolling() {
            refreshStatus();
            pollInterval = setInterval(refreshStatus, 5000); // 每5秒刷新一次
        }

        // 停止轮询
        function stopPolling() {
            if (pollInterval) {
                clearInterval(pollInterval);
                pollInterval = null;
            }
        }

        // 刷新状态
        function refreshStatus() {
            if (!currentTaskId) return;

            fetch(`/index/checkQueueStatus?task_id=${currentTaskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatus(data);
                        
                        // 如果任务完成，停止轮询
                        if (data.status !== 'processing') {
                            stopPolling();
                        }
                    } else {
                        showError(data.message);
                        stopPolling();
                    }
                })
                .catch(error => {
                    console.error('获取状态失败：', error);
                    showError('获取状态失败，请稍后重试');
                });
        }

        // 更新状态显示
        function updateStatus(data) {
            // 更新基本信息
            if (data.query_info) {
                document.getElementById('queryName').textContent = data.query_info.name || '-';
                document.getElementById('submitTime').textContent = data.query_info.created_at || '-';
            }

            // 更新进度
            if (data.progress) {
                const progress = data.progress;
                const percentage = progress.percentage || 0;
                
                document.getElementById('progressText').textContent = `${percentage.toFixed(1)}%`;
                document.getElementById('progressFill').style.width = `${percentage}%`;
                document.getElementById('completedCount').textContent = progress.completed || 0;
                document.getElementById('totalCount').textContent = progress.total || 0;
            }

            // 更新状态消息
            updateStatusMessage(data.status, data.progress);

            // 更新任务列表
            if (data.results) {
                updateTaskList(data.results);
            }

            // 如果任务完成，显示查看结果按钮
            if (data.status === 'completed' || data.status === 'partial_completed') {
                showCompletedActions(data);
            }
        }

        // 更新状态消息
        function updateStatusMessage(status, progress) {
            const messageDiv = document.getElementById('statusMessage');
            const subtitleDiv = document.getElementById('statusSubtitle');
            
            switch (status) {
                case 'processing':
                    messageDiv.className = 'status-message status-processing-msg';
                    messageDiv.innerHTML = `🔄 正在处理查询任务，已完成 ${progress.completed}/${progress.total} 个`;
                    subtitleDiv.textContent = '正在处理您的查询请求...';
                    break;
                case 'completed':
                    messageDiv.className = 'status-message status-completed-msg';
                    messageDiv.innerHTML = '✅ 所有查询任务已完成！';
                    subtitleDiv.textContent = '查询已完成，可以查看结果';
                    break;
                case 'partial_completed':
                    messageDiv.className = 'status-message status-failed-msg';
                    messageDiv.innerHTML = `⚠️ 部分任务完成，${progress.failed} 个任务失败`;
                    subtitleDiv.textContent = '部分查询完成，请查看详情';
                    break;
                default:
                    messageDiv.className = 'status-message status-processing-msg';
                    messageDiv.innerHTML = '🔄 正在初始化查询任务...';
                    subtitleDiv.textContent = '正在准备查询...';
            }
        }

        // 更新任务列表
        function updateTaskList(results) {
            const taskList = document.getElementById('taskList');
            taskList.innerHTML = '';

            // 显示已完成的任务
            if (results.completed) {
                results.completed.forEach(task => {
                    const taskDiv = createTaskItem(task, 'completed');
                    taskList.appendChild(taskDiv);
                });
            }

            // 显示失败的任务
            if (results.failed) {
                results.failed.forEach(task => {
                    const taskDiv = createTaskItem(task, 'failed');
                    taskList.appendChild(taskDiv);
                });
            }
        }

        // 创建任务项
        function createTaskItem(task, status) {
            const div = document.createElement('div');
            div.className = `task-item ${status}`;

            const statusIcon = document.createElement('div');
            statusIcon.className = 'task-status-icon';
            
            let iconText = '';
            let statusClass = '';
            
            switch (status) {
                case 'completed':
                    iconText = '✓';
                    statusClass = 'status-completed';
                    break;
                case 'failed':
                    iconText = '✗';
                    statusClass = 'status-failed';
                    break;
                case 'processing':
                    iconText = '⋯';
                    statusClass = 'status-processing';
                    break;
                default:
                    iconText = '○';
                    statusClass = 'status-pending';
            }
            
            statusIcon.className += ` ${statusClass}`;
            statusIcon.textContent = iconText;

            const taskName = document.createElement('div');
            taskName.className = 'task-name';
            taskName.textContent = task.api_name || task.task_id;

            const taskTime = document.createElement('div');
            taskTime.className = 'task-time';
            taskTime.textContent = task.completed_at || task.failed_at || task.query_time || '';

            div.appendChild(statusIcon);
            div.appendChild(taskName);
            div.appendChild(taskTime);

            return div;
        }

        // 显示完成后的操作按钮
        function showCompletedActions(data) {
            const actionButtons = document.querySelector('.action-buttons');
            
            // 添加查看结果按钮
            if (!document.getElementById('viewResultBtn')) {
                const viewBtn = document.createElement('a');
                viewBtn.id = 'viewResultBtn';
                viewBtn.className = 'btn btn-primary';
                viewBtn.href = '#';
                viewBtn.innerHTML = '📊 查看详细结果';
                viewBtn.onclick = function() {
                    // 构建结果数据并跳转
                    const resultData = {
                        success: true,
                        query_info: data.query_info,
                        results: { results: {} },
                        summary: data.summary || {}
                    };
                    
                    // 转换队列结果格式
                    if (data.results && data.results.completed) {
                        data.results.completed.forEach(task => {
                            resultData.results.results[task.task_id] = {
                                api_name: task.api_name,
                                success: task.success,
                                data: task.data,
                                message: task.message || '查询完成',
                                query_time: task.query_time,
                                price: task.price || 0
                            };
                        });
                    }
                    
                    const encodedData = encodeURIComponent(JSON.stringify(resultData));
                    window.location.href = `/index/queryResult?data=${encodedData}`;
                };
                
                actionButtons.insertBefore(viewBtn, actionButtons.firstChild);
            }
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('statusMessage').className = 'status-message status-failed-msg';
            document.getElementById('statusMessage').innerHTML = `❌ ${message}`;
            document.getElementById('statusSubtitle').textContent = '查询出现错误';
            stopPolling();
        }

        // 页面卸载时停止轮询
        window.addEventListener('beforeunload', function() {
            stopPolling();
        });
    </script>
</body>
</html>
