<?php

namespace apifox;

use apifox\config\ApifoxConfig;

/**
 * Apifox API工厂类
 * 用于创建和管理各种API实例
 */
class ApifoxFactory
{
    /**
     * 创建聘查查API实例
     * @param string $secretKey 可选，如果不传则使用配置文件中的值
     * @return PinccApi
     */
    public static function createPinccApi($secretKey = null)
    {
        $config = ApifoxConfig::getPinccConfig();
        $secretKey = $secretKey ?: $config['secret_key'];
        
        return new PinccApi($secretKey);
    }
    
    /**
     * 快速婚姻状况查询
     * @param string $name 姓名
     * @param string $cardNo 身份证号
     * @return array
     */
    public static function quickMarriageQuery($name, $cardNo)
    {
        $api = self::createPinccApi();
        return $api->marriageQuery($name, $cardNo);
    }
    
    /**
     * 快速学历查询（星耀版）
     * @param string $fullName 姓名
     * @param string $idCardNo 身份证号
     * @return array
     */
    public static function quickEducationQueryV6($fullName, $idCardNo)
    {
        $api = self::createPinccApi();
        return $api->educationQueryV6($fullName, $idCardNo);
    }
    
    /**
     * 快速学历查询（至尊版）
     * @param string $fullName 姓名
     * @param string $idCardNo 身份证号
     * @return array
     */
    public static function quickEducationQueryV4($fullName, $idCardNo)
    {
        $api = self::createPinccApi();
        return $api->educationQueryV4($fullName, $idCardNo);
    }
    
    /**
     * 快速个人失信查询
     * @param string $entityName 姓名
     * @param string $entityNo 身份证号
     * @return array
     */
    public static function quickPersonCreditQuery($entityName, $entityNo)
    {
        $api = self::createPinccApi();
        return $api->creditDishonestyV3($entityName, $entityNo, 1); // 1表示个人
    }
    
    /**
     * 快速企业失信查询
     * @param string $entityName 企业名称
     * @param string $entityNo 企业统一社会信用代码
     * @return array
     */
    public static function quickCompanyCreditQuery($entityName, $entityNo)
    {
        $api = self::createPinccApi();
        return $api->creditDishonestyV3($entityName, $entityNo, 2); // 2表示企业
    }
    
    /**
     * 快速个人名下车辆查询
     * @param string $idCardNo 身份证号
     * @param string $userType 关系类型：1-ETC开户人；2-车辆所有人；3-ETC经办人
     * @param string $fullName 姓名（可选）
     * @return array
     */
    public static function quickPersonalVehicleQuery($idCardNo, $userType = '2', $fullName = '')
    {
        $api = self::createPinccApi();
        return $api->personalVehicle($idCardNo, $userType, $fullName);
    }
    
    /**
     * 快速人企任职记录查询
     * @param string $idCardNo 身份证号
     * @return array
     */
    public static function quickEnterpriseRecordQuery($idCardNo)
    {
        $api = self::createPinccApi();
        return $api->creditEnterpriseV3($idCardNo);
    }
    
    /**
     * 快速个人综合资质评分
     * @param string $fullName 姓名
     * @param string $idCardNo 身份证号
     * @param string $mobile 手机号
     * @return array
     */
    public static function quickQualifyScoreQuery($fullName, $idCardNo, $mobile)
    {
        $api = self::createPinccApi();
        return $api->qualifyScore($fullName, $idCardNo, $mobile);
    }
}
