<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="__CDN__/assets/libs/bootstrap-touch-carousel/bootstrap-touch-carousel.min.css" rel="stylesheet">
    <style>
    /* 轮播图样式优化 */
    .carousel {
        touch-action: pan-y pinch-zoom;
    }
    .carousel .item {
        height: auto; /* 自动高度，适应内容 */
        min-height: auto;
    }
    .carousel .item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .carousel-indicators {
        bottom: 10px;
        margin-bottom: 0;
    }
    .carousel-indicators li {
        margin: 0 3px;
        border-color: rgba(255,255,255,0.7);
        width: 8px;
        height: 8px;
        border-radius: 50%;
    }
    .carousel-indicators .active {
        margin: 0 3px;
        background-color: #fff;
    }

    /* 触摸反馈 */
    .carousel .item a {
        display: block;
        width: 100%;
        height: 100%;
        position: relative;
        -webkit-tap-highlight-color: rgba(0,0,0,0);
    }
    .carousel .item a:active {
        background-color: transparent;
    }

    /* 重置默认样式，消除空白 */
    body {
        margin: 0;
        padding: 0;
    }

    /* 隐藏原有的mainbody */
    #mainbody {
        display: none;
    }

    /* 新的移动端风格样式 */
    .mobile-app-container {
        background: linear-gradient(180deg, #f8fbff 0%, #ffffff 100%);
        padding: 20px 15px;
        padding-bottom: 80px; /* 为底部导航留出空间 */
    }

    .app-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 0 5px;
    }

    .app-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }

    .app-subtitle {
        background: #4285f4;
        color: white;
        padding: 4px 12px;
        border-radius: 12px 3px;
        font-size: 12px;
        margin-left: 10px;
    }

    .status-icons {
        display: flex;
        gap: 5px;
        font-size: 14px;
        color: #666;
    }

    /* 轮播卡片 */
    .main-banner-card {
        background: #fff;
        border-radius: 16px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(235, 235, 235, 0.3);
        min-height: 185px;
        height: 185px;
    }

    .banner-illustration {
        width: 100%;
        height: 100%;
        position: relative;
    }

    .banner-illustration img {
        width: 100%;
        height: 100%;
        border-radius: 16px;
        object-fit: cover;
        border: none;
        outline: none;
    }

    /* 卡片 */
    .couple-report-card {
        background: white;
        border-radius: 16px;
        padding: 10px 20px;
        margin: 20px 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .report-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .report-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-top: 0;
    }

    .report-price {
        text-align: right;
    }

    .current-price {
        font-size: 20px;
        font-weight: bold;
        color: #ff4757;
    }

    .original-price {
        font-size: 14px;
        color: #999;
        text-decoration: line-through;
        position: relative;
        bottom: 5px;
    }

    .report-options {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
    }

    .option-item {
        text-align: center;
        padding: 10px 8px;
        border-radius: 12px;
        background: #f8f9fa;
    }

    .option-item.pink {
        background: linear-gradient(178deg, #fce4ec 0%, #ffffff 100%);
    }

    .option-item.purple {
        background: linear-gradient(178deg, #ce93d8 0%, #ffffff 100%);
    }

    .option-item.blue {
        background: linear-gradient(178deg, #90caf9 0%, #ffffff 100%);
    }

    .option-item.green {
        background: linear-gradient(178deg, #a5d6a7 0%, #ffffff 100%);
    }

    .option-icon {
        width: 32px;
        height: 32px;
        margin: 0 auto 5px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
    }

    .option-text {
        font-size: 12px;
        color: #333;
        font-weight: 500;
    }

    .more-info {
        text-align: center;
        color: #999;
        font-size: 12px;
        margin-top: 10px;
    }

    /* 选取模块区域 */
    .module-selection {
        background: white;
        border-radius: 16px;
        padding: 10px 20px;
        margin: 20px 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .module-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .module-icon {
        width: 24px;
        height: 24px;
        background: #ff4757;
        border-radius: 50%;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
    }

    .module-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-top: 10px;
    }

    .module-item {
        border: 1px solid #eee;
        border-radius: 12px;
        padding: 10px 12px;
        margin-bottom: 10px;
    }

    .module-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }

    .module-checkbox {
        display: flex;
        align-items: center;
    }

    .checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 4px;
        margin-right: 8px;
        position: relative;
        cursor: pointer;
    }

    .checkbox.checked {
        background: #ff4757;
        border-color: #ff4757;
    }

    .checkbox.checked::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .module-name {
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .module-price {
        text-align: right;
    }

    .module-current-price {
        font-size: 18px;
        font-weight: bold;
        color: #ff4757;
    }

    .module-original-price {
        font-size: 12px;
        color: #999;
        text-decoration: line-through;
    }

    .module-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .module-tag {
        background: #fff0f0;
        color: #ff4757;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        border: 1px solid #ffe0e0;
    }

    /* 基本信息区域 */
    .basic-info {
        background: white;
        border-radius: 16px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .info-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .info-link {
        color: #5dade2;
        font-size: 14px;
        text-decoration: none;
    }

    .info-subtitle {
        color: #666;
        font-size: 14px;
        margin-bottom: 20px;
    }



    /* 底部导航 */
    .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-around;
        padding: 8px 0;
        z-index: 1000;
    }

    .nav-item {
        text-align: center;
        color: #999;
        text-decoration: none;
        font-size: 12px;
        flex: 1;
    }

    .nav-item.active {
        color: #4285f4;
    }

    .nav-icon {
        width: 24px;
        height: 24px;
        margin: 0 auto 4px;
        background-size: contain;
    }

    /* 响应式调整 */
    @media (max-width: 480px) {
        .mobile-app-container {
            padding: 15px 10px;
            padding-bottom: 80px;
        }

        /* 小屏幕下保持铺满效果 */
    }
    .hui-s {
        color: #999999;
        font-size: 13px;
    }
    </style>
</head>

<body>

<div class="mobile-app-container">
    <!-- 应用头部 -->
    <div class="app-header">
        <div style="display: flex; align-items: center;">
            <span class="app-title">{$site.name|htmlentities}</span>
            <span class="app-subtitle">看得见的征信</span>
        </div>
    </div>

    <!-- 主要轮播横幅 -->
    {if $hasBanner}
    <div id="carousel-banner" class="carousel slide" data-ride="carousel">
        <div class="carousel-inner">
            {foreach name="bannerList" item="item" key="key"}
            <div class="item {if $key==0}active{/if}">
                <a href="{$item.url}">
                    <div class="main-banner-card">
                        <div class="banner-illustration">
                            <img src="{$item.image|default='__CDN__/assets/img/banner-illustration.svg'}" alt="{$item.title}">
                        </div>
                    </div>
                </a>
            </div>
            {/foreach}
        </div>
        <!-- 轮播指示器 -->
        <ol class="carousel-indicators" style="bottom: 10px;">
            {foreach name="bannerList" item="item" key="key"}
            <li data-target="#carousel-banner" data-slide-to="{$key}" {if $key==0}class="active"{/if}></li>
            {/foreach}
        </ol>
    </div>
    {else}
    <!-- 默认横幅 -->
    <div class="main-banner-card">
        <div class="banner-illustration">
            <img src="__CDN__/assets/img/banner-illustration.svg" alt="默认图片">
        </div>
    </div>
    {/if}

    <!-- 报告 -->
    <div class="couple-report-card">
        <div class="report-header">
            <h3 class="report-title">基础查询报告服务</h3>
            <div class="report-price">
                <div class="current-price">¥ 35.8</div>
                <div class="original-price">¥ 48.8</div>
            </div>
        </div>

        <div class="report-options">
            <div class="option-item pink">
                <div class="option-icon" style="background-image: url('__CDN__/assets/img/nav-home.svg');"></div>
                <div class="option-text">婚姻状态</div>
            </div>
            <div class="option-item purple">
                <div class="option-icon">£</div>
                <div class="option-text">申贷记录</div>
            </div>
            <div class="option-item blue">
                <div class="option-icon">📋</div>
                <div class="option-text">逾期记录</div>
            </div>
            <div class="option-item green">
                <div class="option-icon">⚖️</div>
                <div class="option-text">法院案件</div>
            </div>
        </div>

        <div class="more-info">更多信息请解锁报告～</div>
    </div>

    <!-- 选取模块（可多选） -->
    <div class="module-selection">
        
        <div class="module-header">
            <h3 class="module-title">增值查询服务<span class="hui-s">（可多选，用套餐更优惠哦）</span></h3>
        </div>

        <!-- 综合水平模块 -->
        <div class="module-item">
            <div class="module-item-header">
                <div class="module-checkbox">
                    <div class="checkbox" onclick="toggleCheckbox(this)"></div>
                    <span class="module-name">综合水平</span>
                </div>
                <div class="module-price">
                    <div class="module-current-price">¥ 16.8</div>
                    <div class="module-original-price">¥ 26.8</div>
                </div>
            </div>
            <div class="module-tags">
                <span class="module-tag">收入信息</span>
                <span class="module-tag">车辆信息</span>
                <span class="module-tag">学历信息</span>
            </div>
        </div>

        <!-- 企业信息模块 -->
        <div class="module-item">
            <div class="module-item-header">
                <div class="module-checkbox">
                    <div class="checkbox" onclick="toggleCheckbox(this)"></div>
                    <span class="module-name">企业信息</span>
                </div>
                <div class="module-price">
                    <div class="module-current-price">¥ 45.8</div>
                    <div class="module-original-price">¥ 88.8</div>
                </div>
            </div>
            <div class="module-tags">
                <span class="module-tag">企业法人</span>
                <span class="module-tag">企业高管</span>
                <span class="module-tag">经营风险</span>
                <span class="module-tag">关联企业</span>
                <span class="module-tag">学历信息</span>
            </div>
        </div>

        <div class="info-header">
            <h3 class="info-title">基本信息</h3>
            <a href="#" class="info-link">报告示例 ></a>
        </div>
        <div class="info-subtitle">为保证数据准确，请输入真实信息</div>
    </div>

</div>

<!-- 底部导航 -->
<div class="bottom-nav">
    <a href="#" class="nav-item active">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-home.svg');"></div>
        <div>首页</div>
    </a>
    <!--a href="#" class="nav-item">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-report.svg');"></div>
        <div>数聚报告广场</div>
    </a-->
    <a href="#" class="nav-item">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-mine.svg');"></div>
        <div>我的</div>
    </a>
</div>

<!-- 引入JS文件 -->
<script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>
<script src="__CDN__/assets/libs/bootstrap/dist/js/bootstrap.min.js"></script>
{if $hasBanner}
<script>
$(function() {
    // 初始化轮播
    var $carousel = $('#carousel-banner');

    // 设置自动播放
    $carousel.carousel({
        interval: 4000,  // 4秒自动切换
        pause: 'hover'   // 鼠标悬停时暂停
    });

    // 移动端触摸支持
    var startX = 0;
    var startY = 0;
    var threshold = 50; // 滑动阈值

    $carousel.on('touchstart', function(e) {
        var touch = e.originalEvent.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
        $(this).carousel('pause');
    });

    $carousel.on('touchmove', function(e) {
        e.preventDefault(); // 防止页面滚动
    });

    $carousel.on('touchend', function(e) {
        var touch = e.originalEvent.changedTouches[0];
        var deltaX = touch.clientX - startX;
        var deltaY = touch.clientY - startY;

        // 确保是水平滑动
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > threshold) {
            if (deltaX > 0) {
                $(this).carousel('prev');
            } else {
                $(this).carousel('next');
            }
        }

        // 恢复自动播放
        $(this).carousel('cycle');
    });


});
</script>
{/if}

<script>
// 复选框切换功能
function toggleCheckbox(element) {
    element.classList.toggle('checked');
}
</script>

</body>
</html>


