<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="__CDN__/assets/libs/bootstrap-touch-carousel/bootstrap-touch-carousel.min.css" rel="stylesheet">
    <style>
    /* 轮播图样式优化 */
    .carousel {
        touch-action: pan-y pinch-zoom;
        margin-bottom: 30px;
    }
    .carousel .item {
        height: 300px; /* 移动端友好的高度 */
    }
    .carousel .item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .carousel-caption {
        background: rgba(0,0,0,0.5);
        padding: 10px;
        border-radius: 5px;
        bottom: 20px;
    }
    .carousel-caption h3 {
        margin: 0;
        font-size: 18px;
    }
    .carousel-indicators {
        bottom: 0;
    }
    .carousel-indicators li {
        margin: 0 3px;
        border-color: rgba(255,255,255,0.7);
    }
    .carousel-indicators .active {
        margin: 0 3px;
        background-color: #fff;
    }
    
    /* 响应式调整 */
    @media (min-width: 768px) {
        .carousel .item {
            height: 400px;
        }
        .carousel-caption h3 {
            font-size: 22px;
        }
    }
    
    /* 触摸反馈 */
    .carousel .item a {
        display: block;
        width: 100%;
        height: 100%;
        position: relative;
        -webkit-tap-highlight-color: rgba(0,0,0,0);
    }
    .carousel .item a:active {
        background-color: transparent;
    }

    .new-banner-style {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(0, 102, 255, 0.08);
        margin-bottom: 15px;
    }
    .banner-bg {
        background: linear-gradient(90deg, #4faaff 0%, #eaf4ff 100%);
        position: relative;
        min-height: 160px;
        display: flex;
        align-items: center;
    }
    .banner-img {
        width: 120px;
        height: 120px;
        object-fit: contain;
        margin-left: 20px;
    }
    .carousel-caption.new-caption {
        position: absolute;
        left: 160px;
        top: 30px;
        text-align: left;
        background: none;
        color: #222;
        padding: 0;
    }
    .banner-badge {
        display: inline-block;
        background: #ff6b00;
        color: #fff;
        font-size: 12px;
        border-radius: 4px;
        padding: 2px 8px;
        margin-bottom: 8px;
    }
    .banner-title {
        font-size: 22px;
        font-weight: bold;
        color: #222;
        margin-bottom: 12px;
        line-height: 1.3;
    }
    .banner-btn {
        display: inline-block;
        background: #fff;
        color: #ff6b00;
        font-weight: bold;
        border-radius: 20px;
        padding: 4px 16px;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(255,107,0,0.08);
        text-decoration: none;
    }
    .banner-btn:hover {
        background: #ff6b00;
        color: #fff;
    }
    @media (max-width: 768px) {
        .carousel-caption.new-caption {
            left: 110px;
            top: 18px;
        }
        .banner-title {
            font-size: 16px;
        }
        .banner-img {
            width: 80px;
            height: 80px;
            margin-left: 10px;
        }
    }
    </style>
</head>

<body>

{if $hasBanner}
<!-- 轮播图开始 -->
<div id="carousel-banner" class="carousel slide touch-carousel new-banner-style" data-ride="carousel-touch">
    <!-- 指示器 -->
    <ol class="carousel-indicators">
        {foreach name="bannerList" item="item" key="key"}
        <li data-target="#carousel-banner" data-slide-to="{$key}" {if $key==0}class="active"{/if}></li>
        {/foreach}
    </ol>
    <!-- 轮播项目 -->
    <div class="carousel-inner" role="listbox">
        {foreach name="bannerList" item="item" key="key"}
        <div class="item {if $key==0}active{/if}">
            <a href="{$item.url}" target="_blank">
                <div class="banner-bg">
                    <img src="{$item.image}" alt="{$item.title}" class="banner-img">
                    <div class="carousel-caption new-caption">
                        <div class="banner-badge">{$item.badge|default='更实惠'}</div>
                        <h3 class="banner-title">{$item.title|raw}</h3>
                        <a href="{$item.url}" class="banner-btn">{$item.button_text|default='立即联系'}</a>
                    </div>
                </div>
            </a>
        </div>
        {/foreach}
    </div>
</div>
<!-- 轮播图结束 -->
{/if}

<div id="mainbody">
    <div class="container">
        <div class="text-center">
            <h1>{$site.name|htmlentities}</h1>
            <a href="{:url('index/user/index', '', false, true)}">{:__('Member center')}</a>
        </div>
    </div>
</div>

<div class="footer">
    <div class="container">
        <p>Copyright @ {$site.name|htmlentities} {:date('Y',time())} 版权所有 <a href="https://beian.miit.gov.cn" target="_blank">{$site.beian|htmlentities}</a></p>
    </div>
</div>

<!-- 引入JS文件 -->
<script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>
<script src="__CDN__/assets/libs/bootstrap/dist/js/bootstrap.min.js"></script>
{if $hasBanner}
<script src="__CDN__/assets/libs/bootstrap-touch-carousel/bootstrap-touch-carousel.min.js"></script>
<script>
$(function() {
    // 初始化触摸轮播
    var $carousel = $('#carousel-banner');
    
    $carousel.touchCarousel({
        interval: 3000,  // 自动播放间隔时间（毫秒）
        swipe: 30       // 降低触发滑动的最小距离，使滑动更灵敏
    });
    
    // 优化触摸体验
    $carousel
        .on('touchstart', function(e) {
            // 开始触摸时暂停自动播放
            $(this).carousel('pause');
        })
        .on('touchend', function(e) {
            // 触摸结束后恢复自动播放
            $(this).carousel('cycle');
        });
        
    // 防止轮播图区域的触摸事件影响整个页面滚动
    $carousel.on('touchmove', function(e) {
        if (e.touches && e.touches.length === 1) {
            e.preventDefault();
        }
    });
});
</script>
{/if}

</body>
</html>


