<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="__CDN__/assets/libs/bootstrap-touch-carousel/bootstrap-touch-carousel.min.css" rel="stylesheet">
    <style>
    /* 轮播图样式优化 */
    .carousel {
        touch-action: pan-y pinch-zoom;
    }
    .carousel .item {
        height: auto; /* 自动高度，适应内容 */
        min-height: auto;
    }
    .carousel .item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .carousel-indicators {
        bottom: 10px;
        margin-bottom: 0;
    }
    .carousel-indicators li {
        margin: 0 3px;
        border-color: rgba(255,255,255,0.7);
        width: 8px;
        height: 8px;
        border-radius: 50%;
    }
    .carousel-indicators .active {
        margin: 0 3px;
        background-color: #fff;
    }

    /* 触摸反馈 */
    .carousel .item a {
        display: block;
        width: 100%;
        height: 100%;
        position: relative;
        -webkit-tap-highlight-color: rgba(0,0,0,0);
    }
    .carousel .item a:active {
        background-color: transparent;
    }

    /* 重置默认样式，消除空白 */
    body {
        margin: 0;
        padding: 0;
    }

    /* 隐藏原有的mainbody */
    #mainbody {
        display: none;
    }

    /* 新的移动端风格样式 */
    .mobile-app-container {
        background: linear-gradient(180deg, #f8fbff 0%, #ffffff 100%);
        padding: 20px 15px;
        padding-bottom: 80px; /* 为底部导航留出空间 */
    }

    .app-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 0 5px;
    }

    .app-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }

    .app-subtitle {
        background: #4285f4;
        color: white;
        padding: 4px 12px;
        border-radius: 12px 3px;
        font-size: 12px;
        margin-left: 10px;
    }

    .status-icons {
        display: flex;
        gap: 5px;
        font-size: 14px;
        color: #666;
    }

    /* 轮播卡片 */
    .main-banner-card {
        background: #fff;
        border-radius: 16px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(235, 235, 235, 0.3);
        min-height: 185px;
        height: 185px;
    }

    .banner-illustration {
        width: 100%;
        height: 100%;
        position: relative;
    }

    .banner-illustration img {
        width: 100%;
        height: 100%;
        border-radius: 16px;
        object-fit: cover;
        border: none;
        outline: none;
    }

    /* 卡片 */
    .couple-report-card {
        background: white;
        border-radius: 16px;
        padding: 10px 20px;
        margin: 20px 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .report-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .report-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-top: 0;
    }

    .report-price {
        text-align: right;
    }

    .current-price {
        font-size: 20px;
        font-weight: bold;
        color: #ff4757;
    }

    .original-price {
        font-size: 14px;
        color: #999;
        text-decoration: line-through;
        position: relative;
        bottom: 5px;
    }

    .report-options {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
    }

    .option-item {
        text-align: center;
        padding: 10px 8px;
        border-radius: 12px;
        background: #f8f9fa;
    }

    .option-item.pink {
        background: linear-gradient(178deg, #fce4ec 0%, #ffffff 100%);
    }

    .option-item.purple {
        background: linear-gradient(178deg, #ce93d8 0%, #ffffff 100%);
    }

    .option-item.blue {
        background: linear-gradient(178deg, #90caf9 0%, #ffffff 100%);
    }

    .option-item.green {
        background: linear-gradient(178deg, #a5d6a7 0%, #ffffff 100%);
    }

    .option-icon {
        width: 32px;
        height: 32px;
        margin: 0 auto 5px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
    }

    .option-text {
        font-size: 12px;
        color: #333;
        font-weight: 500;
    }

    .more-info {
        text-align: center;
        color: #999;
        font-size: 12px;
        margin-top: 10px;
    }

    /* 选取模块区域 */
    .module-selection {
        background: white;
        border-radius: 16px;
        padding: 10px 20px;
        margin: 20px 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .module-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .module-icon {
        width: 24px;
        height: 24px;
        background: #ff4757;
        border-radius: 50%;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
    }

    .module-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-top: 10px;
    }

    .module-item {
        border: 1px solid #eee;
        border-radius: 12px;
        padding: 10px 12px;
        margin-bottom: 10px;
    }

    .module-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }

    .module-checkbox {
        display: flex;
        align-items: center;
    }

    .checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 4px;
        margin-right: 8px;
        position: relative;
        cursor: pointer;
    }

    .checkbox.checked {
        background: #ff4757;
        border-color: #ff4757;
    }

    .checkbox.checked::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .module-name {
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .module-price {
        text-align: right;
    }

    .module-current-price {
        font-size: 18px;
        font-weight: bold;
        color: #ff4757;
    }

    .module-original-price {
        font-size: 12px;
        color: #999;
        text-decoration: line-through;
    }

    .module-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .module-tag {
        background: #fff0f0;
        color: #ff4757;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        border: 1px solid #ffe0e0;
    }

    /* 基本信息区域 */
    .basic-info {
        background: white;
        border-radius: 16px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .info-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .info-link {
        color: #5dade2;
        font-size: 14px;
        text-decoration: none;
    }

    .info-subtitle {
        color: #666;
        font-size: 14px;
        margin-bottom: 20px;
    }

    /* 单项/套餐切换菜单 */
    .tab-menu {
        display: flex;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 4px;
        margin-bottom: 20px;
    }

    .tab-item {
        flex: 1;
        text-align: center;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .tab-item.active {
        background: white;
        color: #ff4757;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 内容区域 */
    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    /* 单项选择样式 */
    .single-item {
        border: 1px solid #eee;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .single-item:hover {
        border-color: #ff4757;
        background: #fff8f8;
    }

    .single-item.selected {
        border-color: #ff4757;
        background: #fff0f0;
    }

    .single-item-left {
        display: flex;
        align-items: center;
    }

    .single-item-checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 4px;
        margin-right: 12px;
        position: relative;
        transition: all 0.3s ease;
    }

    .single-item.selected .single-item-checkbox {
        background: #ff4757;
        border-color: #ff4757;
    }

    .single-item.selected .single-item-checkbox::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .single-item-info h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .single-item-info p {
        margin: 0;
        font-size: 12px;
        color: #999;
    }

    .single-item-price {
        text-align: right;
    }

    .single-item-current {
        font-size: 16px;
        font-weight: bold;
        color: #ff4757;
    }

    .single-item-original {
        font-size: 12px;
        color: #999;
        text-decoration: line-through;
    }

    /* 表单输入样式 */
    .form-group {
        margin-bottom: 16px;
    }

    .form-input {
        width: 100%;
        padding: 16px;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        font-size: 16px;
        background: #f8f9fa;
        color: #333;
        box-sizing: border-box;
        transition: all 0.3s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: #ff4757;
        background: white;
        box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
    }

    .form-input::placeholder {
        color: #999;
        font-size: 16px;
    }

    /* 图标输入框 */
    .input-with-icon {
        position: relative;
    }

    .input-icon {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        font-size: 18px;
        pointer-events: none;
    }

    .input-with-icon .form-input {
        padding-left: 50px;
    }

    /* 协议同意 */
    .agreement-section {
        margin: 20px 0;
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }

    .agreement-checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 4px;
        position: relative;
        cursor: pointer;
        flex-shrink: 0;
        margin-top: 2px;
        transition: all 0.3s ease;
    }

    .agreement-checkbox.checked {
        background: #ff4757;
        border-color: #ff4757;
    }

    .agreement-checkbox.checked::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .agreement-text {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
    }

    .agreement-link {
        color: #5dade2;
        text-decoration: none;
    }

    .agreement-link:hover {
        text-decoration: underline;
    }

    /* 查询按钮 */
    .query-button {
        width: 100%;
        padding: 16px;
        background: #bbb;
        color: white;
        border: none;
        border-radius: 25px;
        font-size: 18px;
        font-weight: bold;
        cursor: not-allowed;
        transition: all 0.3s ease;
        margin-top: 20px;
    }

    .query-button.active {
        background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
    }

    .query-button.active:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
    }

    .query-button:active {
        transform: translateY(0);
    }



    /* 底部导航 */
    .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-around;
        padding: 8px 0;
        z-index: 1000;
    }

    .nav-item {
        text-align: center;
        color: #999;
        text-decoration: none;
        font-size: 12px;
        flex: 1;
    }

    .nav-item.active {
        color: #4285f4;
    }

    .nav-icon {
        width: 24px;
        height: 24px;
        margin: 0 auto 4px;
        background-size: contain;
    }

    /* 响应式调整 */
    @media (max-width: 480px) {
        .mobile-app-container {
            padding: 15px 10px;
            padding-bottom: 80px;
        }

        /* 小屏幕下保持铺满效果 */
    }
    .hui-s {
        color: #999999;
        font-size: 13px;
    }
    </style>
</head>

<body>

<div class="mobile-app-container">
    <!-- 应用头部 -->
    <div class="app-header">
        <div style="display: flex; align-items: center;">
            <span class="app-title">{$site.name|htmlentities}</span>
            <span class="app-subtitle">看得见的征信</span>
        </div>
    </div>

    <!-- 主要轮播横幅 -->
    {if $hasBanner}
    <div id="carousel-banner" class="carousel slide" data-ride="carousel">
        <div class="carousel-inner">
            {foreach name="bannerList" item="item" key="key"}
            <div class="item {if $key==0}active{/if}">
                <a href="{$item.url}">
                    <div class="main-banner-card">
                        <div class="banner-illustration">
                            <img src="{$item.image|default='__CDN__/assets/img/banner-illustration.svg'}" alt="{$item.title}">
                        </div>
                    </div>
                </a>
            </div>
            {/foreach}
        </div>
        <!-- 轮播指示器 -->
        <ol class="carousel-indicators" style="bottom: 10px;">
            {foreach name="bannerList" item="item" key="key"}
            <li data-target="#carousel-banner" data-slide-to="{$key}" {if $key==0}class="active"{/if}></li>
            {/foreach}
        </ol>
    </div>
    {else}
    <!-- 默认横幅 -->
    <div class="main-banner-card">
        <div class="banner-illustration">
            <img src="__CDN__/assets/img/banner-illustration.svg" alt="默认图片">
        </div>
    </div>
    {/if}

    <!-- 报告 -->
    <div class="couple-report-card">
        <div class="report-header">
            <h3 class="report-title">基础查询报告服务</h3>
            <div class="report-price">
                <div class="current-price">¥ 35.8</div>
                <div class="original-price">¥ 48.8</div>
            </div>
        </div>

        <div class="report-options">
            <div class="option-item pink">
                <div class="option-icon" style="background-image: url('__CDN__/assets/img/nav-home.svg');"></div>
                <div class="option-text">婚姻状态</div>
            </div>
            <div class="option-item purple">
                <div class="option-icon">£</div>
                <div class="option-text">申贷记录</div>
            </div>
            <div class="option-item blue">
                <div class="option-icon">📋</div>
                <div class="option-text">逾期记录</div>
            </div>
            <div class="option-item green">
                <div class="option-icon">⚖️</div>
                <div class="option-text">法院案件</div>
            </div>
        </div>

        <div class="more-info">更多信息请解锁报告～</div>
    </div>

    <!-- 选取模块（可多选） -->
    <div class="module-selection">

        <div class="module-header">
            <div class="module-icon">📋</div>
            <h3 class="module-title">选取模块（可多选）</h3>
        </div>

        <!-- 单项/套餐切换菜单 -->
        <div class="tab-menu">
            <div class="tab-item active" onclick="switchTab('single')">单项</div>
            <div class="tab-item" onclick="switchTab('package')">套餐</div>
        </div>

        <!-- 单项选择内容 -->
        <div id="single-content" class="tab-content active">
            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>基础信息查询</h4>
                        <p>身份验证、手机号验证</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 5.8</div>
                    <div class="single-item-original">¥ 8.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>收入信息</h4>
                        <p>工资收入、其他收入来源</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 8.8</div>
                    <div class="single-item-original">¥ 12.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>车辆信息</h4>
                        <p>车辆登记、车辆价值评估</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 6.8</div>
                    <div class="single-item-original">¥ 9.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>学历信息</h4>
                        <p>学历验证、学校信息</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 4.8</div>
                    <div class="single-item-original">¥ 7.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>企业法人</h4>
                        <p>法人代表信息、企业注册信息</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 12.8</div>
                    <div class="single-item-original">¥ 18.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>经营风险</h4>
                        <p>企业风险评估、经营状况</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 15.8</div>
                    <div class="single-item-original">¥ 22.8</div>
                </div>
            </div>
        </div>

        <!-- 套餐选择内容 -->
        <div id="package-content" class="tab-content">
            <!-- 综合水平模块 -->
            <div class="module-item">
                <div class="module-item-header">
                    <div class="module-checkbox">
                        <div class="checkbox" onclick="toggleCheckbox(this)"></div>
                        <span class="module-name">综合水平</span>
                    </div>
                    <div class="module-price">
                        <div class="module-current-price">¥ 16.8</div>
                        <div class="module-original-price">¥ 26.8</div>
                    </div>
                </div>
                <div class="module-tags">
                    <span class="module-tag">收入信息</span>
                    <span class="module-tag">车辆信息</span>
                    <span class="module-tag">学历信息</span>
                </div>
            </div>

            <!-- 企业信息模块 -->
            <div class="module-item">
                <div class="module-item-header">
                    <div class="module-checkbox">
                        <div class="checkbox" onclick="toggleCheckbox(this)"></div>
                        <span class="module-name">企业信息</span>
                    </div>
                    <div class="module-price">
                        <div class="module-current-price">¥ 45.8</div>
                        <div class="module-original-price">¥ 88.8</div>
                    </div>
                </div>
                <div class="module-tags">
                    <span class="module-tag">企业法人</span>
                    <span class="module-tag">企业高管</span>
                    <span class="module-tag">经营风险</span>
                    <span class="module-tag">关联企业</span>
                    <span class="module-tag">学历信息</span>
                </div>
            </div>

            <!-- 全套服务模块 -->
            <div class="module-item">
                <div class="module-item-header">
                    <div class="module-checkbox">
                        <div class="checkbox" onclick="toggleCheckbox(this)"></div>
                        <span class="module-name">全套服务</span>
                    </div>
                    <div class="module-price">
                        <div class="module-current-price">¥ 88.8</div>
                        <div class="module-original-price">¥ 158.8</div>
                    </div>
                </div>
                <div class="module-tags">
                    <span class="module-tag">基础信息</span>
                    <span class="module-tag">收入信息</span>
                    <span class="module-tag">车辆信息</span>
                    <span class="module-tag">学历信息</span>
                    <span class="module-tag">企业信息</span>
                    <span class="module-tag">风险评估</span>
                </div>
            </div>
        </div>
        <!-- 基本信息 -->
        <div class="info-header">
            <h3 class="info-title">基本信息</h3>
            <a href="#" class="info-link">报告示例 ></a>
        </div>
        <div class="info-subtitle">为保证数据准确，请输入真实信息</div>

        <!-- 表单区域 -->
        <form id="queryForm">
            <!-- 姓名输入 -->
            <div class="form-group">
                <div class="input-with-icon">
                    <div class="input-icon">👤</div>
                    <input type="text" class="form-input" id="userName" placeholder="请输入本人姓名" required>
                </div>
            </div>

            <!-- 身份证输入 -->
            <div class="form-group">
                <div class="input-with-icon">
                    <div class="input-icon">🆔</div>
                    <input type="text" class="form-input" id="idCard" placeholder="请输入本人身份证" maxlength="18" required>
                </div>
            </div>

            <!-- 手机号输入 -->
            <div class="form-group">
                <div class="input-with-icon">
                    <div class="input-icon">📱</div>
                    <input type="tel" class="form-input" id="phoneNumber" placeholder="请输入本人手机号" maxlength="11" required>
                </div>
            </div>

            <!-- 协议同意 -->
            <div class="agreement-section">
                <div class="agreement-checkbox" id="agreementCheck" onclick="toggleAgreement()"></div>
                <div class="agreement-text">
                    我已阅读并同意 <a href="#" class="agreement-link">《用户协议》</a> <a href="#" class="agreement-link">《隐私政策》</a>
                </div>
            </div>

            <!-- 查询按钮 -->
            <button type="submit" class="query-button" id="queryBtn" disabled>
                35.8 元查询
            </button>
        </form>
    </div>

</div>

<!-- 底部导航 -->
<div class="bottom-nav">
    <a href="#" class="nav-item active">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-home.svg');"></div>
        <div>首页</div>
    </a>
    <!--a href="#" class="nav-item">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-report.svg');"></div>
        <div>数聚报告广场</div>
    </a-->
    <a href="#" class="nav-item">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-mine.svg');"></div>
        <div>我的</div>
    </a>
</div>

<!-- 引入JS文件 -->
<script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>
<script src="__CDN__/assets/libs/bootstrap/dist/js/bootstrap.min.js"></script>
{if $hasBanner}
<script>
$(function() {
    // 初始化轮播
    var $carousel = $('#carousel-banner');

    // 设置自动播放
    $carousel.carousel({
        interval: 4000,  // 4秒自动切换
        pause: 'hover'   // 鼠标悬停时暂停
    });

    // 移动端触摸支持
    var startX = 0;
    var startY = 0;
    var threshold = 50; // 滑动阈值

    $carousel.on('touchstart', function(e) {
        var touch = e.originalEvent.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
        $(this).carousel('pause');
    });

    $carousel.on('touchmove', function(e) {
        e.preventDefault(); // 防止页面滚动
    });

    $carousel.on('touchend', function(e) {
        var touch = e.originalEvent.changedTouches[0];
        var deltaX = touch.clientX - startX;
        var deltaY = touch.clientY - startY;

        // 确保是水平滑动
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > threshold) {
            if (deltaX > 0) {
                $(this).carousel('prev');
            } else {
                $(this).carousel('next');
            }
        }

        // 恢复自动播放
        $(this).carousel('cycle');
    });


});
</script>
{/if}

<script>
// 复选框切换功能
function toggleCheckbox(element) {
    element.classList.toggle('checked');
}

// 单项/套餐切换功能
function switchTab(tabType) {
    // 移除所有tab的active状态
    document.querySelectorAll('.tab-item').forEach(item => {
        item.classList.remove('active');
    });

    // 隐藏所有内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // 激活当前tab
    event.target.classList.add('active');

    // 显示对应内容
    if (tabType === 'single') {
        document.getElementById('single-content').classList.add('active');
    } else if (tabType === 'package') {
        document.getElementById('package-content').classList.add('active');
    }
}

// 单项选择切换功能
function toggleSingleItem(element) {
    element.classList.toggle('selected');
    updateQueryButton();
}

// 协议同意切换功能
function toggleAgreement() {
    const checkbox = document.getElementById('agreementCheck');
    checkbox.classList.toggle('checked');
    updateQueryButton();
}

// 更新查询按钮状态
function updateQueryButton() {
    const userName = document.getElementById('userName').value.trim();
    const idCard = document.getElementById('idCard').value.trim();
    const phoneNumber = document.getElementById('phoneNumber').value.trim();
    const agreementChecked = document.getElementById('agreementCheck').classList.contains('checked');
    const queryBtn = document.getElementById('queryBtn');

    // 验证身份证格式（简单验证）
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;

    // 检查所有条件
    const isValid = userName.length >= 2 &&
                   idCardRegex.test(idCard) &&
                   phoneRegex.test(phoneNumber) &&
                   agreementChecked;

    if (isValid) {
        queryBtn.classList.add('active');
        queryBtn.disabled = false;
    } else {
        queryBtn.classList.remove('active');
        queryBtn.disabled = true;
    }
}

// 表单提交处理
document.addEventListener('DOMContentLoaded', function() {
    // 监听输入框变化
    document.getElementById('userName').addEventListener('input', updateQueryButton);
    document.getElementById('idCard').addEventListener('input', updateQueryButton);
    document.getElementById('phoneNumber').addEventListener('input', updateQueryButton);

    // 监听表单提交
    document.getElementById('queryForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const userName = document.getElementById('userName').value.trim();
        const idCard = document.getElementById('idCard').value.trim();
        const phoneNumber = document.getElementById('phoneNumber').value.trim();

        // 这里可以添加实际的查询逻辑
        alert(`查询信息：\n姓名：${userName}\n身份证：${idCard}\n手机号：${phoneNumber}`);
    });

    // 身份证输入格式化
    document.getElementById('idCard').addEventListener('input', function(e) {
        let value = e.target.value.replace(/[^\dXx]/g, '');
        if (value.length > 18) {
            value = value.slice(0, 18);
        }
        e.target.value = value.toUpperCase();
    });

    // 手机号输入格式化
    document.getElementById('phoneNumber').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 11) {
            value = value.slice(0, 11);
        }
        e.target.value = value;
    });
});
</script>

</body>
</html>


