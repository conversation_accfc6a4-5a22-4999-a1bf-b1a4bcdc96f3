<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="__CDN__/assets/libs/bootstrap-touch-carousel/bootstrap-touch-carousel.min.css" rel="stylesheet">
    <style>
    /* 轮播图样式优化 */
    .carousel {
        touch-action: pan-y pinch-zoom;
        margin-bottom: 30px;
    }
    .carousel .item {
        height: 300px; /* 移动端友好的高度 */
    }
    .carousel .item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .carousel-caption {
        background: rgba(0,0,0,0.5);
        padding: 10px;
        border-radius: 5px;
        bottom: 20px;
    }
    .carousel-caption h3 {
        margin: 0;
        font-size: 18px;
    }
    .carousel-indicators {
        bottom: 0;
    }
    .carousel-indicators li {
        margin: 0 3px;
        border-color: rgba(255,255,255,0.7);
    }
    .carousel-indicators .active {
        margin: 0 3px;
        background-color: #fff;
    }
    
    /* 响应式调整 */
    @media (min-width: 768px) {
        .carousel .item {
            height: 400px;
        }
        .carousel-caption h3 {
            font-size: 22px;
        }
    }
    
    /* 触摸反馈 */
    .carousel .item a {
        display: block;
        width: 100%;
        height: 100%;
        position: relative;
        -webkit-tap-highlight-color: rgba(0,0,0,0);
    }
    .carousel .item a:active {
        background-color: transparent;
    }

    /* 新的移动端风格样式 */
    .mobile-app-container {
        background: linear-gradient(180deg, #f8fbff 0%, #ffffff 100%);
        padding: 20px 15px;
        min-height: 100vh;
    }

    .app-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 0 5px;
    }

    .app-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }

    .app-subtitle {
        background: #4285f4;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        margin-left: 10px;
    }

    .status-icons {
        display: flex;
        gap: 5px;
        font-size: 14px;
        color: #666;
    }

    /* 主要轮播卡片 */
    .main-banner-card {
        background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
        color: white;
        box-shadow: 0 8px 24px rgba(66, 133, 244, 0.3);
    }

    .banner-badge {
        background: #ff6b00;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 12px;
    }

    .banner-title {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 8px;
        line-height: 1.3;
    }

    .banner-subtitle {
        font-size: 16px;
        margin-bottom: 16px;
        opacity: 0.9;
    }

    .banner-btn {
        background: white;
        color: #ff6b00;
        padding: 8px 20px;
        border-radius: 20px;
        font-weight: bold;
        text-decoration: none;
        display: inline-block;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .banner-illustration {
        position: absolute;
        right: 20px;
        top: 20px;
        width: 120px;
        height: 120px;
        opacity: 0.8;
    }

    /* 查询服务区域 */
    .query-section {
        margin-bottom: 20px;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .section-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }

    .section-more {
        color: #999;
        font-size: 14px;
        text-decoration: none;
    }

    .query-info {
        background: #f8f9fa;
        padding: 12px 16px;
        border-radius: 12px;
        margin-bottom: 15px;
        font-size: 14px;
        color: #666;
    }

    /* 服务卡片网格 */
    .service-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 20px;
    }

    .service-card {
        background: white;
        border-radius: 16px;
        padding: 20px 16px;
        text-align: center;
        box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        position: relative;
        overflow: hidden;
        text-decoration: none;
        color: inherit;
        transition: transform 0.2s ease;
    }

    .service-card:hover {
        transform: translateY(-2px);
        text-decoration: none;
        color: inherit;
    }

    .service-card.blue {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    }

    .service-card.pink {
        background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
    }

    .service-card.cyan {
        background: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%);
    }

    .service-card.orange {
        background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
    }

    .service-card.green {
        background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    }

    .service-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 12px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }

    .service-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
    }

    .service-subtitle {
        font-size: 12px;
        color: #666;
        margin-bottom: 12px;
    }

    .service-btn {
        background: #4285f4;
        color: white;
        padding: 6px 16px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: bold;
        border: none;
        cursor: pointer;
    }

    .service-card.pink .service-btn {
        background: #e91e63;
    }

    .service-card.cyan .service-btn {
        background: #00bcd4;
    }

    /* 小微企业和家政服务 */
    .bottom-services {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 20px;
    }

    .bottom-service-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        text-decoration: none;
        color: inherit;
    }

    .bottom-service-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
    }

    .bottom-service-subtitle {
        font-size: 12px;
        color: #999;
        margin-bottom: 12px;
    }

    .bottom-service-btn {
        background: #ff9800;
        color: white;
        padding: 6px 16px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: bold;
        border: none;
    }

    .bottom-service-card:nth-child(2) .bottom-service-btn {
        background: #4caf50;
    }

    /* 推广奖励区域 */
    .promotion-section {
        background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
        border-radius: 16px;
        padding: 20px;
        color: white;
        text-align: center;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }

    .promotion-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .promotion-subtitle {
        font-size: 14px;
        opacity: 0.9;
        margin-bottom: 16px;
    }

    .promotion-btn {
        background: white;
        color: #ff6b9d;
        padding: 8px 20px;
        border-radius: 20px;
        font-weight: bold;
        text-decoration: none;
        display: inline-block;
        font-size: 14px;
    }

    /* 底部导航 */
    .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-around;
        padding: 8px 0;
        z-index: 1000;
    }

    .nav-item {
        text-align: center;
        color: #999;
        text-decoration: none;
        font-size: 12px;
        flex: 1;
    }

    .nav-item.active {
        color: #4285f4;
    }

    .nav-icon {
        width: 24px;
        height: 24px;
        margin: 0 auto 4px;
        background-size: contain;
    }

    /* 响应式调整 */
    @media (max-width: 480px) {
        .mobile-app-container {
            padding: 15px 10px;
        }

        .service-grid {
            gap: 8px;
        }

        .service-card {
            padding: 16px 12px;
        }

        .banner-illustration {
            width: 100px;
            height: 100px;
        }
    }
    </style>
</head>

<body>

<div class="mobile-app-container">
    <!-- 应用头部 -->
    <div class="app-header">
        <div style="display: flex; align-items: center;">
            <span class="app-title">贝融助手</span>
            <span class="app-subtitle">看得见的征信</span>
        </div>
        <div class="status-icons">
            <span>09:18</span>
        </div>
    </div>

    <!-- 主要轮播横幅 -->
    {if $hasBanner}
    <div id="carousel-banner" class="carousel slide" data-ride="carousel">
        <div class="carousel-inner">
            {foreach name="bannerList" item="item" key="key"}
            <div class="item {if $key==0}active{/if}">
                <div class="main-banner-card">
                    <div class="banner-badge">{$item.badge|default='更实惠'}</div>
                    <div class="banner-title">{$item.title|default='入职背调报告'}</div>
                    <div class="banner-subtitle">{$item.subtitle|default='企业采购入口'}</div>
                    <a href="{$item.url}" class="banner-btn">{$item.button_text|default='立即联系 >>'}</a>
                    <div class="banner-illustration">
                        <img src="{$item.image|default='__CDN__/assets/img/banner-illustration.svg'}" alt="{$item.title}" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                </div>
            </div>
            {/foreach}
        </div>
        <!-- 轮播指示器 -->
        <ol class="carousel-indicators" style="bottom: 10px;">
            {foreach name="bannerList" item="item" key="key"}
            <li data-target="#carousel-banner" data-slide-to="{$key}" {if $key==0}class="active"{/if}></li>
            {/foreach}
        </ol>
    </div>
    {else}
    <!-- 默认横幅 -->
    <div class="main-banner-card">
        <div class="banner-badge">更实惠</div>
        <div class="banner-title">入职背调报告</div>
        <div class="banner-subtitle">企业采购入口</div>
        <a href="#" class="banner-btn">立即联系 >></a>
        <div class="banner-illustration">
            <img src="__CDN__/assets/img/banner-illustration.svg" alt="背调报告" style="width: 100%; height: 100%; object-fit: contain;">
        </div>
    </div>
    {/if}

    <!-- 查询服务区域 -->
    <div class="query-section">
        <div class="section-header">
            <h3 class="section-title">查询服务</h3>
            <a href="#" class="section-more">报告解读 ></a>
        </div>
        <div class="query-info">
            刚刚 183****3337 这个用户查询了员工个人风险报告
        </div>
    </div>

    <!-- 服务卡片网格 -->
    <div class="service-grid">
        <a href="#" class="service-card blue">
            <div class="service-icon" style="background-image: url('__CDN__/assets/img/icon-personal-risk.svg');"></div>
            <div class="service-title">个人风险</div>
            <div class="service-subtitle">信任不止一面</div>
            <button class="service-btn">GO ></button>
        </a>

        <a href="#" class="service-card pink">
            <div class="service-icon" style="background-image: url('__CDN__/assets/img/icon-emotion-report.svg');"></div>
            <div class="service-title">情侣报告</div>
            <div class="service-subtitle">感受两个未来，未语可知心</div>
            <button class="service-btn">GO ></button>
        </a>

        <a href="#" class="service-card cyan">
            <div class="service-icon" style="background-image: url('__CDN__/assets/img/icon-background-check.svg');"></div>
            <div class="service-title">入职背调</div>
            <div class="service-subtitle">深度背调，用人无忧</div>
            <button class="service-btn">GO ></button>
        </a>
    </div>

    <!-- 底部服务 -->
    <div class="bottom-services">
        <a href="#" class="bottom-service-card">
            <div class="bottom-service-title">小微企业</div>
            <div class="bottom-service-subtitle">信用报告</div>
            <button class="bottom-service-btn">GO ></button>
        </a>

        <a href="#" class="bottom-service-card">
            <div class="bottom-service-title">家政服务</div>
            <div class="bottom-service-subtitle">了解热爱的陌生人</div>
            <button class="bottom-service-btn">GO ></button>
        </a>
    </div>

    <!-- 推广奖励区域 -->
    <div class="promotion-section">
        <div class="promotion-title">推广奖励</div>
        <div class="promotion-subtitle">动动手指，让流量变现</div>
        <div style="font-size: 24px; font-weight: bold; margin: 10px 0;">双人成行，双倍试信</div>
        <div style="font-size: 12px; opacity: 0.8; margin-bottom: 15px;">保持专属推广，你的格调属于你</div>
        <a href="#" class="promotion-btn">立即抢购</a>
        <!-- 装饰性投诉按钮 -->
        <div style="position: absolute; top: 15px; right: 15px; background: rgba(255,255,255,0.2); border-radius: 12px; padding: 4px 8px; font-size: 12px;">
            🛡️ 投诉
        </div>
    </div>
</div>

<!-- 底部导航 -->
<div class="bottom-nav">
    <a href="#" class="nav-item active">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-home.svg');"></div>
        <div>首页</div>
    </a>
    <a href="#" class="nav-item">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-report.svg');"></div>
        <div>数聚报告广场</div>
    </a>
    <a href="#" class="nav-item">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-mine.svg');"></div>
        <div>我的</div>
    </a>
</div>

<div class="footer">
    <div class="container">
        <p>Copyright @ {$site.name|htmlentities} {:date('Y',time())} 版权所有 <a href="https://beian.miit.gov.cn" target="_blank">{$site.beian|htmlentities}</a></p>
    </div>
</div>

<!-- 引入JS文件 -->
<script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>
<script src="__CDN__/assets/libs/bootstrap/dist/js/bootstrap.min.js"></script>
{if $hasBanner}
<script>
$(function() {
    // 初始化轮播
    var $carousel = $('#carousel-banner');

    // 设置自动播放
    $carousel.carousel({
        interval: 4000,  // 4秒自动切换
        pause: 'hover'   // 鼠标悬停时暂停
    });

    // 移动端触摸支持
    var startX = 0;
    var startY = 0;
    var threshold = 50; // 滑动阈值

    $carousel.on('touchstart', function(e) {
        var touch = e.originalEvent.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
        $(this).carousel('pause');
    });

    $carousel.on('touchmove', function(e) {
        e.preventDefault(); // 防止页面滚动
    });

    $carousel.on('touchend', function(e) {
        var touch = e.originalEvent.changedTouches[0];
        var deltaX = touch.clientX - startX;
        var deltaY = touch.clientY - startY;

        // 确保是水平滑动
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > threshold) {
            if (deltaX > 0) {
                $(this).carousel('prev');
            } else {
                $(this).carousel('next');
            }
        }

        // 恢复自动播放
        $(this).carousel('cycle');
    });

    // 添加卡片点击效果
    $('.service-card, .bottom-service-card').on('touchstart', function() {
        $(this).css('transform', 'scale(0.98)');
    }).on('touchend touchcancel', function() {
        $(this).css('transform', 'scale(1)');
    });
});
</script>
{/if}

</body>
</html>


