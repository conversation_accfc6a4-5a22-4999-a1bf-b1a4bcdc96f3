<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="__CDN__/assets/libs/bootstrap-touch-carousel/bootstrap-touch-carousel.min.css" rel="stylesheet">
</head>

<body>

    <!-- 主要轮播横幅 -->
    {if $hasBanner}
    <div id="carousel-banner" class="carousel slide" data-ride="carousel">
        <div class="carousel-inner">
            {foreach name="bannerList" item="item" key="key"}
            <div class="item {if $key==0}active{/if}">
                <a href="{$item.url}">
                    <div class="main-banner-card">
                        <div class="banner-illustration">
                            <img src="{$item.image|default='__CDN__/assets/img/banner-illustration.svg'}" alt="{$item.title}">
                        </div>
                    </div>
                </a>
            </div>
            {/foreach}
        </div>
        <!-- 轮播指示器 -->
        <ol class="carousel-indicators" style="bottom: 10px;">
            {foreach name="bannerList" item="item" key="key"}
            <li data-target="#carousel-banner" data-slide-to="{$key}" {if $key==0}class="active"{/if}></li>
            {/foreach}
        </ol>
    </div>
    {else}
    <!-- 默认横幅 -->
    <div class="main-banner-card">
        <div class="banner-illustration">
            <img src="__CDN__/assets/img/banner-illustration.svg" alt="默认图片">
        </div>
    </div>
    {/if}

<div class="mobile-app-container">

    <!-- 报告 -->
    <div class="couple-report-card">
        <div class="report-header">
            <h3 class="report-title">基础查询报告服务</h3>
            <div class="report-price">
                <div class="current-price">¥ 35.8</div>
                <div class="original-price">¥ 48.8</div>
            </div>
        </div>

        <div class="report-options">
            <div class="option-item pink">
                <div class="option-icon">
                    <img src="__CDN__/assets/img/icon-love.svg" alt="婚姻状态">
                </div>
                <div class="option-text">婚姻状态</div>
            </div>
            <div class="option-item purple">
                <div class="option-icon">
                    <img src="__CDN__/assets/img/icon-loan.svg" alt="申贷记录">
                </div>
                <div class="option-text">申贷记录</div>
            </div>
            <div class="option-item blue">
                <div class="option-icon">
                    <img src="__CDN__/assets/img/icon-personal-risks.svg" alt="逾期记录">
                </div>
                <div class="option-text">逾期记录</div>
            </div>
            <div class="option-item green">
                <div class="option-icon">
                    <img src="__CDN__/assets/img/icon-court.svg" alt="法院案件">
                </div>
                <div class="option-text">法院案件</div>
            </div>
        </div>

        <div class="more-info">更多信息请解锁报告～</div>
    </div>

    <!-- 选取模块（可多选） -->
    <div class="module-selection">

        <div class="module-header">
            <h3 class="module-title">选取模块（可多选）</h3>
        </div>

        <!-- 单项/套餐切换菜单 -->
        <div class="tab-menu">
            <div class="tab-item active" onclick="switchTab('single')">单项</div>
            <div class="tab-item" onclick="switchTab('package')">套餐</div>
        </div>

        <!-- 单项选择内容 -->
        <div id="single-content" class="tab-content active">
            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>基础信息查询</h4>
                        <p>身份验证、手机号验证</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 5.8</div>
                    <div class="single-item-original">¥ 8.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>收入信息</h4>
                        <p>工资收入、其他收入来源</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 8.8</div>
                    <div class="single-item-original">¥ 12.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>车辆信息</h4>
                        <p>车辆登记、车辆价值评估</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 6.8</div>
                    <div class="single-item-original">¥ 9.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>学历信息</h4>
                        <p>学历验证、学校信息</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 4.8</div>
                    <div class="single-item-original">¥ 7.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>企业法人</h4>
                        <p>法人代表信息、企业注册信息</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 12.8</div>
                    <div class="single-item-original">¥ 18.8</div>
                </div>
            </div>

            <div class="single-item" onclick="toggleSingleItem(this)">
                <div class="single-item-left">
                    <div class="single-item-checkbox"></div>
                    <div class="single-item-info">
                        <h4>经营风险</h4>
                        <p>企业风险评估、经营状况</p>
                    </div>
                </div>
                <div class="single-item-price">
                    <div class="single-item-current">¥ 15.8</div>
                    <div class="single-item-original">¥ 22.8</div>
                </div>
            </div>
        </div>

        <!-- 套餐选择内容 -->
        <div id="package-content" class="tab-content">
            <!-- 综合水平模块 -->
            <div class="module-item">
                <div class="module-item-header">
                    <div class="module-checkbox">
                        <div class="checkbox" onclick="toggleCheckbox(this)"></div>
                        <span class="module-name">综合水平</span>
                    </div>
                    <div class="module-price">
                        <div class="module-current-price">¥ 16.8</div>
                        <div class="module-original-price">¥ 26.8</div>
                    </div>
                </div>
                <div class="module-tags">
                    <span class="module-tag">收入信息</span>
                    <span class="module-tag">车辆信息</span>
                    <span class="module-tag">学历信息</span>
                </div>
            </div>

            <!-- 企业信息模块 -->
            <div class="module-item">
                <div class="module-item-header">
                    <div class="module-checkbox">
                        <div class="checkbox" onclick="toggleCheckbox(this)"></div>
                        <span class="module-name">企业信息</span>
                    </div>
                    <div class="module-price">
                        <div class="module-current-price">¥ 45.8</div>
                        <div class="module-original-price">¥ 88.8</div>
                    </div>
                </div>
                <div class="module-tags">
                    <span class="module-tag">企业法人</span>
                    <span class="module-tag">企业高管</span>
                    <span class="module-tag">经营风险</span>
                    <span class="module-tag">关联企业</span>
                    <span class="module-tag">学历信息</span>
                </div>
            </div>

            <!-- 全套服务模块 -->
            <div class="module-item">
                <div class="module-item-header">
                    <div class="module-checkbox">
                        <div class="checkbox" onclick="toggleCheckbox(this)"></div>
                        <span class="module-name">全套服务</span>
                    </div>
                    <div class="module-price">
                        <div class="module-current-price">¥ 88.8</div>
                        <div class="module-original-price">¥ 158.8</div>
                    </div>
                </div>
                <div class="module-tags">
                    <span class="module-tag">基础信息</span>
                    <span class="module-tag">收入信息</span>
                    <span class="module-tag">车辆信息</span>
                    <span class="module-tag">学历信息</span>
                    <span class="module-tag">企业信息</span>
                    <span class="module-tag">风险评估</span>
                </div>
            </div>
        </div>
        <!-- 基本信息 -->
        <div class="info-header">
            <h3 class="info-title">基本信息</h3>
            <a href="/test.html" class="info-link">报告示例 ></a>
        </div>
        <div class="info-subtitle">为保证数据准确，请输入真实信息</div>

        <!-- 表单区域 -->
        <form id="queryForm">
            <!-- 姓名输入 -->
            <div class="form-group">
                <div class="input-with-icon">
                    <div class="input-icon">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    </div>
                    <input type="text" class="form-input" id="userName" placeholder="请输入本人姓名" required>
                </div>
            </div>

            <!-- 身份证输入 -->
            <div class="form-group">
                <div class="input-with-icon">
                    <div class="input-icon">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4V6h16v12zM6 8h2v2H6V8zm0 3h2v2H6v-2zm0 3h2v2H6v-2zm3-6h7v2H9V8zm0 3h7v2H9v-2zm0 3h4v2H9v-2z"/>
                        </svg>
                    </div>
                    <input type="text" class="form-input" id="idCard" placeholder="请输入本人身份证" maxlength="18" required>
                </div>
            </div>

            <!-- 手机号输入 -->
            <div class="form-group">
                <div class="input-with-icon">
                    <div class="input-icon">
                        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z"/>
                            <circle cx="12" cy="16.5" r="1.5"/>
                        </svg>
                    </div>
                    <input type="tel" class="form-input" id="phoneNumber" placeholder="请输入本人手机号" maxlength="11" required>
                </div>
            </div>

            <!-- 查询按钮 -->
            <button type="submit" class="query-button" id="queryBtn" disabled>
                <span>12万人用户已查</span>
                查询
            </button>

            <!-- 协议同意 -->
            <div class="agreement-section">
                <div class="agreement-checkbox" id="agreementCheck" onclick="toggleAgreement()"></div>
                <div class="agreement-text">
                    我已阅读并同意 <a href="/agreement1.html" class="agreement-link">《用户协议》</a> 及 <a href="/agreement2.html" class="agreement-link">《隐私政策》</a>
                </div>
            </div>

            <div class="agreement-section">
                <div class="agreement-checkbox" id="agreementCheck2" onclick="toggleAgreement2()"></div>
                <div class="agreement-text">
                    我已同意授权查询 <a href="/agreement3.html" class="agreement-link">《信用授权书》</a> 及 <a href="/agreement4.html" class="agreement-link">《个人数据承诺书》</a>
                </div>
            </div>

            <div class="agreement-section">
                <div class="agreement-checkbox" id="agreementCheck3" onclick="toggleAgreement3()"></div>
                <div class="agreement-text" style="color: #5dade2;">
                    本产品提供数据查询服务，没交易不可撤销
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 底部导航 -->
<div class="bottom-nav">
    <a href="#" class="nav-item active">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-home.svg');"></div>
        <div>首页</div>
    </a>
    <a href="#" class="nav-item">
        <div class="nav-icon" style="background-image: url('__CDN__/assets/img/nav-mine.svg');"></div>
        <div>我的</div>
    </a>
</div>

<!-- 引入JS文件 -->
<script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>
<script src="__CDN__/assets/libs/bootstrap/dist/js/bootstrap.min.js"></script>
{if $hasBanner}
<script>
$(function() {
    // 初始化轮播
    var $carousel = $('#carousel-banner');

    // 设置自动播放
    $carousel.carousel({
        interval: 4000,  // 4秒自动切换
        pause: 'hover'   // 鼠标悬停时暂停
    });

    // 移动端触摸支持
    var startX = 0;
    var startY = 0;
    var threshold = 50; // 滑动阈值

    $carousel.on('touchstart', function(e) {
        var touch = e.originalEvent.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
        $(this).carousel('pause');
    });

    $carousel.on('touchmove', function(e) {
        e.preventDefault(); // 防止页面滚动
    });

    $carousel.on('touchend', function(e) {
        var touch = e.originalEvent.changedTouches[0];
        var deltaX = touch.clientX - startX;
        var deltaY = touch.clientY - startY;

        // 确保是水平滑动
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > threshold) {
            if (deltaX > 0) {
                $(this).carousel('prev');
            } else {
                $(this).carousel('next');
            }
        }

        // 恢复自动播放
        $(this).carousel('cycle');
    });


});
</script>
{/if}

<script>
// 复选框切换功能
function toggleCheckbox(element) {
    element.classList.toggle('checked');
}

// 单项/套餐切换功能
function switchTab(tabType) {
    // 移除所有tab的active状态
    document.querySelectorAll('.tab-item').forEach(item => {
        item.classList.remove('active');
    });

    // 隐藏所有内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // 激活当前tab
    event.target.classList.add('active');

    // 显示对应内容
    if (tabType === 'single') {
        document.getElementById('single-content').classList.add('active');
    } else if (tabType === 'package') {
        document.getElementById('package-content').classList.add('active');
    }
}

// 单项选择切换功能
function toggleSingleItem(element) {
    element.classList.toggle('selected');
    updateQueryButton();
}

// 协议同意切换功能
function toggleAgreement() {
    const checkbox = document.getElementById('agreementCheck');
    checkbox.classList.toggle('checked');
    updateQueryButton();
}

// 第二个协议同意切换功能
function toggleAgreement2() {
    const checkbox = document.getElementById('agreementCheck2');
    checkbox.classList.toggle('checked');
    updateQueryButton();
}

// 第三个协议同意切换功能
function toggleAgreement3() {
    const checkbox = document.getElementById('agreementCheck3');
    checkbox.classList.toggle('checked');
    updateQueryButton();
}

// 更新查询按钮状态
function updateQueryButton() {
    const userName = document.getElementById('userName').value.trim();
    const idCard = document.getElementById('idCard').value.trim();
    const phoneNumber = document.getElementById('phoneNumber').value.trim();
    const agreementChecked = document.getElementById('agreementCheck').classList.contains('checked');
    const agreementChecked2 = document.getElementById('agreementCheck2').classList.contains('checked');
    const agreementChecked3 = document.getElementById('agreementCheck3').classList.contains('checked');
    const queryBtn = document.getElementById('queryBtn');

    // 验证身份证格式（简单验证）
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;

    // 检查所有条件（包括所有协议都必须同意）
    const isValid = userName.length >= 2 &&
                   idCardRegex.test(idCard) &&
                   phoneRegex.test(phoneNumber) &&
                   agreementChecked &&
                   agreementChecked2 &&
                   agreementChecked3;

    if (isValid) {
        queryBtn.classList.add('active');
        queryBtn.disabled = false;
    } else {
        queryBtn.classList.remove('active');
        queryBtn.disabled = true;
    }
}

// 表单提交处理
document.addEventListener('DOMContentLoaded', function() {
    // 监听输入框变化
    document.getElementById('userName').addEventListener('input', updateQueryButton);
    document.getElementById('idCard').addEventListener('input', updateQueryButton);
    document.getElementById('phoneNumber').addEventListener('input', updateQueryButton);

    // 监听表单提交
    document.getElementById('queryForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const userName = document.getElementById('userName').value.trim();
        const idCard = document.getElementById('idCard').value.trim();
        const phoneNumber = document.getElementById('phoneNumber').value.trim();

        // 这里可以添加实际的查询逻辑
        alert(`查询信息：\n姓名：${userName}\n身份证：${idCard}\n手机号：${phoneNumber}`);
    });

    // 身份证输入格式化
    document.getElementById('idCard').addEventListener('input', function(e) {
        let value = e.target.value.replace(/[^\dXx]/g, '');
        if (value.length > 18) {
            value = value.slice(0, 18);
        }
        e.target.value = value.toUpperCase();
    });

    // 手机号输入格式化
    document.getElementById('phoneNumber').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 11) {
            value = value.slice(0, 11);
        }
        e.target.value = value;
    });
});
</script>

</body>
</html>


