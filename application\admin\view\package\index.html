<div class="panel panel-default panel-intro">
    {include file="common/search" /}

    <div class="panel-heading">
        <ul class="nav nav-tabs" data-field="status">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="statusList" item="vo" key="key"}
            <li><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="t-all">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('package/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('package/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('package/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-import {:$auth->check('package/import')?'':'hide'}" title="{:__('Import')}" id="btn-import-file" data-url="ajax/upload" data-mimetype="csv,xls,xlsx" data-multiple="false"><i class="fa fa-upload"></i> {:__('Import')}</a>

                        <div class="dropdown btn-group {:$auth->check('package/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>

                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('package/edit')}"
                           data-operate-del="{:$auth->check('package/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<script>
$(function () {
    // 初始化表格参数
    var options = {
        extend: {
            index_url: 'package/index' + location.search,
            add_url: 'package/add',
            edit_url: 'package/edit',
            del_url: 'package/del',
            multi_url: 'package/multi',
            import_url: 'package/import',
            table: 'package',
        },
        columns: [
            [
                {checkbox: true},
                {field: 'id', title: __('Id')},
                {field: 'name', title: __('Name'), operate: 'LIKE'},
                {field: 'description', title: __('Description'), operate: 'LIKE'},
                {field: 'type', title: __('Type'), searchList: {:json_encode($typeList)}, formatter: Table.api.formatter.normal},
                {field: 'current_price', title: __('Current price'), operate:'BETWEEN'},
                {field: 'original_price', title: __('Original price'), operate:'BETWEEN'},
                {field: 'tags', title: __('Tags'), operate: 'LIKE'},
                {field: 'weigh', title: __('Weigh'), operate: false},
                {field: 'status', title: __('Status'), searchList: {:json_encode($statusList)}, formatter: Table.api.formatter.status},
                {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
            ]
        ]
    };

    // 初始化表格
    var table = $("#table");

    table.bootstrapTable(options);

    // 为表格绑定事件
    Table.api.bindevent(table);
});
</script>
                </div>
            </div>

        </div>
    </div>
</div>
