<?php

namespace apifox\config;

/**
 * Apifox API配置类
 */
class ApifoxConfig
{
    /**
     * 聘查查API配置
     */
    const PINCC_API = [
        'app_id' => 'your_app_id',        // 替换为实际的appid
        'secret' => 'your_secret_key',    // 替换为实际的密钥
        'secret_key' => 'your_secret_key', // 兼容旧版本，请替换为实际的密钥
        'base_url' => 'https://api.edazi.com/api',
        'timeout' => 30,                  // 请求超时时间（秒）
        'debug' => false,                 // 是否开启调试模式
    ];
    
    /**
     * 获取聘查查API配置
     * @return array
     */
    public static function getPinccConfig()
    {
        return self::PINCC_API;
    }

    /**
     * 获取APP ID
     * @return string
     */
    public static function getAppId()
    {
        return self::get('app_id', self::PINCC_API['app_id']);
    }

    /**
     * 获取密钥
     * @return string
     */
    public static function getSecret()
    {
        return self::get('secret', self::PINCC_API['secret']);
    }

    /**
     * 获取API基础URL
     * @return string
     */
    public static function getBaseUrl()
    {
        return self::get('base_url', self::PINCC_API['base_url']);
    }

    /**
     * 获取请求超时时间
     * @return int
     */
    public static function getTimeout()
    {
        return self::get('timeout', self::PINCC_API['timeout']);
    }

    /**
     * 是否开启调试模式
     * @return bool
     */
    public static function isDebug()
    {
        return self::get('debug', self::PINCC_API['debug']);
    }

    /**
     * 从配置文件或环境变量获取配置
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        // 优先从环境变量获取
        $envKey = 'PINCC_' . strtoupper($key);
        $envValue = getenv($envKey);
        if ($envValue !== false) {
            // 处理布尔值
            if ($envValue === 'true') return true;
            if ($envValue === 'false') return false;
            // 处理数字
            if (is_numeric($envValue)) return (int)$envValue;
            return $envValue;
        }

        // 从配置数组获取
        $config = self::PINCC_API;
        return isset($config[$key]) ? $config[$key] : $default;
    }

    /**
     * 验证配置是否完整
     * @return array
     */
    public static function validateConfig()
    {
        $errors = [];

        if (self::getAppId() === 'your_app_id') {
            $errors[] = 'APP ID 未配置，请设置实际的 app_id';
        }

        if (self::getSecret() === 'your_secret_key') {
            $errors[] = '密钥未配置，请设置实际的 secret';
        }

        if (empty(self::getBaseUrl())) {
            $errors[] = 'API基础URL未配置';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
