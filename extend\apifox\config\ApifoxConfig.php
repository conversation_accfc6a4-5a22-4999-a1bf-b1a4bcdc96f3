<?php

namespace apifox\config;

/**
 * Apifox API配置类
 */
class ApifoxConfig
{
    /**
     * 聘查查API配置
     */
    const PINCC_API = [
        'secret_key' => 'your_secret_key', // 请替换为实际的密钥
        'base_url' => 'https://api.edazi.com/api',
    ];
    
    /**
     * 获取聘查查API配置
     * @return array
     */
    public static function getPinccConfig()
    {
        return self::PINCC_API;
    }
    
    /**
     * 从配置文件或环境变量获取配置
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        // 优先从环境变量获取
        $envValue = getenv($key);
        if ($envValue !== false) {
            return $envValue;
        }
        
        // 从配置数组获取
        $config = self::PINCC_API;
        return isset($config[$key]) ? $config[$key] : $default;
    }
}
