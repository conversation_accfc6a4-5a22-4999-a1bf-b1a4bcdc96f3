<?php

namespace apifox;

use apifox\config\ApifoxConfig;

/**
 * 聘查查API接口类
 * 用于调用聘查查第三方查询API
 */
class PinccApi
{
    // API配置
    private $baseUrl;
    private $secretKey;
    private $appId;
    private $timeout;
    private $debug;

    /**
     * 构造函数
     * @param string $secretKey 密钥（可选，优先使用配置文件）
     * @param string $appId APP ID（可选，优先使用配置文件）
     */
    public function __construct($secretKey = null, $appId = null)
    {
        // 优先使用传入的参数，否则使用配置文件
        $this->secretKey = $secretKey ?: ApifoxConfig::getSecret();
        $this->appId = $appId ?: ApifoxConfig::getAppId();
        $this->baseUrl = ApifoxConfig::getBaseUrl();
        $this->timeout = ApifoxConfig::getTimeout();
        $this->debug = ApifoxConfig::isDebug();

        // 验证必要配置
        if (empty($this->secretKey) || $this->secretKey === 'your_secret_key') {
            throw new \Exception('API密钥未配置，请在 ApifoxConfig 中设置正确的 secret');
        }

        if (empty($this->appId) || $this->appId === 'your_app_id') {
            throw new \Exception('APP ID未配置，请在 ApifoxConfig 中设置正确的 app_id');
        }
    }
    
    /**
     * 全国婚姻查询
     * @param string $name 姓名
     * @param string $cardNo 身份证号
     * @return array
     */
    public function marriageQuery($name, $cardNo)
    {
        $url = $this->baseUrl . '/Pincc/marriage';
        
        $params = [
            'name' => $name,
            'card_no' => $cardNo
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 学历查询（星耀版）
     * @param string $fullName 姓名
     * @param string $idCardNo 身份证号
     * @return array
     */
    public function educationQueryV6($fullName, $idCardNo)
    {
        $url = $this->baseUrl . '/JH/verifyV6';
        
        $params = [
            'fullName' => $fullName,
            'idCardNo' => $idCardNo
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 学历查询（至尊版）（限量）
     * @param string $fullName 姓名
     * @param string $idCardNo 身份证号
     * @return array
     */
    public function educationQueryV4($fullName, $idCardNo)
    {
        $url = $this->baseUrl . '/JH/verifyV4';
        
        $params = [
            'fullName' => $fullName,
            'idCardNo' => $idCardNo
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 失信模型（实时版）
     * @param string $entityName 姓名/企业名称
     * @param string $entityNo 身份证号/企业统一社会信用代码
     * @param int $entityType 实体类型:1-个人;2-企业
     * @return array
     */
    public function creditDishonestyV3($entityName, $entityNo, $entityType)
    {
        $url = $this->baseUrl . '/JH/creditDishonestyV3';
        
        $params = [
            'entityName' => $entityName,
            'entityNo' => $entityNo,
            'entityType' => $entityType
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 个人名下车辆查询
     * @param string $idCardNo 身份证号
     * @param string $userType 关系类型：1-ETC开户人；2-车辆所有人；3-ETC经办人
     * @param string $fullName 姓名（可选）
     * @param int $vehicleType 车辆类型：0-客车；1-货车；2-全部；默认查全部（可选）
     * @return array
     */
    public function personalVehicle($idCardNo, $userType, $fullName = '', $vehicleType = 2)
    {
        $url = $this->baseUrl . '/JH/personalVehicle';
        
        $params = [
            'idCardNo' => $idCardNo,
            'userType' => $userType,
            'vehicleType' => $vehicleType
        ];
        
        if (!empty($fullName)) {
            $params['fullName'] = $fullName;
        }
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 人企任职记录查询
     * @param string $idCardNo 身份证号
     * @return array
     */
    public function creditEnterpriseV3($idCardNo)
    {
        $url = $this->baseUrl . '/JH/creditEnterpriseV3';
        
        $params = [
            'idCardNo' => $idCardNo
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 个人综合资质评分
     * @param string $fullName 姓名
     * @param string $idCardNo 身份证号
     * @param string $mobile 手机号
     * @return array
     */
    public function qualifyScore($fullName, $idCardNo, $mobile)
    {
        $url = $this->baseUrl . '/JH/qualifyScore';
        
        $params = [
            'fullName' => $fullName,
            'idCardNo' => $idCardNo,
            'mobile' => $mobile
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 发送HTTP请求
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @return array
     */
    private function sendRequest($url, $params)
    {
        // 构建请求头
        $headers = [
            'secret-key: ' . $this->secretKey,
            'app-id: ' . $this->appId,
            'Content-Type: application/json',
        ];

        // 调试模式输出请求信息
        if ($this->debug) {
            error_log("API请求: " . $url);
            error_log("请求参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            error_log("请求头: " . json_encode($headers, JSON_UNESCAPED_UNICODE));
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params, JSON_UNESCAPED_UNICODE));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'PinccApi/1.0');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);

        // 调试模式输出响应信息
        if ($this->debug) {
            error_log("HTTP状态码: " . $httpCode);
            error_log("响应时间: " . $info['total_time'] . "秒");
            error_log("响应内容: " . $response);
        }

        if ($error) {
            $errorMsg = 'CURL错误: ' . $error;
            if ($this->debug) {
                error_log($errorMsg);
            }
            return [
                'success' => false,
                'message' => $errorMsg,
                'data' => null
            ];
        }

        if ($httpCode !== 200) {
            $errorMsg = 'HTTP错误: ' . $httpCode;
            if ($this->debug) {
                error_log($errorMsg);
            }
            return [
                'success' => false,
                'message' => $errorMsg,
                'data' => null
            ];
        }

        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $errorMsg = 'JSON解析错误: ' . json_last_error_msg();
            if ($this->debug) {
                error_log($errorMsg);
                error_log("原始响应: " . $response);
            }
            return [
                'success' => false,
                'message' => $errorMsg,
                'data' => $response
            ];
        }

        return [
            'success' => true,
            'message' => '请求成功',
            'data' => $result
        ];
    }

    /**
     * 获取当前配置信息
     * @return array
     */
    public function getConfig()
    {
        return [
            'app_id' => $this->appId,
            'secret' => substr($this->secretKey, 0, 8) . '***', // 只显示前8位
            'base_url' => $this->baseUrl,
            'timeout' => $this->timeout,
            'debug' => $this->debug
        ];
    }
}
