<?php

namespace apifox;

/**
 * 聘查查API接口类
 * 用于调用聘查查第三方查询API
 */
class PinccApi
{
    // API配置
    private $baseUrl = 'https://api.edazi.com/api';
    private $secretKey;
    
    /**
     * 构造函数
     * @param string $secretKey 密钥
     */
    public function __construct($secretKey)
    {
        $this->secretKey = $secretKey;
    }
    
    /**
     * 全国婚姻查询
     * @param string $name 姓名
     * @param string $cardNo 身份证号
     * @return array
     */
    public function marriageQuery($name, $cardNo)
    {
        $url = $this->baseUrl . '/Pincc/marriage';
        
        $params = [
            'name' => $name,
            'card_no' => $cardNo
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 学历查询（星耀版）
     * @param string $fullName 姓名
     * @param string $idCardNo 身份证号
     * @return array
     */
    public function educationQueryV6($fullName, $idCardNo)
    {
        $url = $this->baseUrl . '/JH/verifyV6';
        
        $params = [
            'fullName' => $fullName,
            'idCardNo' => $idCardNo
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 学历查询（至尊版）（限量）
     * @param string $fullName 姓名
     * @param string $idCardNo 身份证号
     * @return array
     */
    public function educationQueryV4($fullName, $idCardNo)
    {
        $url = $this->baseUrl . '/JH/verifyV4';
        
        $params = [
            'fullName' => $fullName,
            'idCardNo' => $idCardNo
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 失信模型（实时版）
     * @param string $entityName 姓名/企业名称
     * @param string $entityNo 身份证号/企业统一社会信用代码
     * @param int $entityType 实体类型:1-个人;2-企业
     * @return array
     */
    public function creditDishonestyV3($entityName, $entityNo, $entityType)
    {
        $url = $this->baseUrl . '/JH/creditDishonestyV3';
        
        $params = [
            'entityName' => $entityName,
            'entityNo' => $entityNo,
            'entityType' => $entityType
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 个人名下车辆查询
     * @param string $idCardNo 身份证号
     * @param string $userType 关系类型：1-ETC开户人；2-车辆所有人；3-ETC经办人
     * @param string $fullName 姓名（可选）
     * @param int $vehicleType 车辆类型：0-客车；1-货车；2-全部；默认查全部（可选）
     * @return array
     */
    public function personalVehicle($idCardNo, $userType, $fullName = '', $vehicleType = 2)
    {
        $url = $this->baseUrl . '/JH/personalVehicle';
        
        $params = [
            'idCardNo' => $idCardNo,
            'userType' => $userType,
            'vehicleType' => $vehicleType
        ];
        
        if (!empty($fullName)) {
            $params['fullName'] = $fullName;
        }
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 人企任职记录查询
     * @param string $idCardNo 身份证号
     * @return array
     */
    public function creditEnterpriseV3($idCardNo)
    {
        $url = $this->baseUrl . '/JH/creditEnterpriseV3';
        
        $params = [
            'idCardNo' => $idCardNo
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 个人综合资质评分
     * @param string $fullName 姓名
     * @param string $idCardNo 身份证号
     * @param string $mobile 手机号
     * @return array
     */
    public function qualifyScore($fullName, $idCardNo, $mobile)
    {
        $url = $this->baseUrl . '/JH/qualifyScore';
        
        $params = [
            'fullName' => $fullName,
            'idCardNo' => $idCardNo,
            'mobile' => $mobile
        ];
        
        return $this->sendRequest($url, $params);
    }
    
    /**
     * 发送HTTP请求
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @return array
     */
    private function sendRequest($url, $params)
    {
        // 构建请求头
        $headers = [
            'secret-key: ' . $this->secretKey,
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'message' => 'CURL错误: ' . $error,
                'data' => null
            ];
        }
        
        if ($httpCode !== 200) {
            return [
                'success' => false,
                'message' => 'HTTP错误: ' . $httpCode,
                'data' => null
            ];
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => 'JSON解析错误: ' . json_last_error_msg(),
                'data' => $response
            ];
        }
        
        return [
            'success' => true,
            'message' => '请求成功',
            'data' => $result
        ];
    }
}
