<?php

namespace app\admin\command\Install;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Exception;

class Banner extends Command
{
    protected function configure()
    {
        $this->setName('install:banner')
            ->setDescription('Install Banner module');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln("Installing Banner module...");
        
        try {
            // 创建轮播图表
            $this->createBannerTable($output);
            
            // 添加权限节点
            $this->addAuthRules($output);
            
            // 添加示例数据
            $this->addSampleData($output);
            
            $output->writeln("Banner module installed successfully!");
        } catch (Exception $e) {
            $output->error("Installation failed: " . $e->getMessage());
        }
    }
    
    /**
     * 创建轮播图表
     */
    protected function createBannerTable(Output $output)
    {
        $output->writeln("Creating banner table...");
        
        // 检查表是否已存在
        if (Db::query("SHOW TABLES LIKE '" . config('database.prefix') . "banner'")) {
            $output->writeln("Table banner already exists, skipping...");
            return;
        }
        
        // 创建表
        $sql = "CREATE TABLE `" . config('database.prefix') . "banner` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
            `image` varchar(255) NOT NULL DEFAULT '' COMMENT '图片',
            `url` varchar(255) NOT NULL DEFAULT '' COMMENT '链接',
            `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
            `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
            `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
            `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `weigh` (`weigh`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';";
        
        Db::execute($sql);
        $output->writeln("Banner table created successfully!");
    }
    
    /**
     * 添加权限节点
     */
    protected function addAuthRules(Output $output)
    {
        $output->writeln("Adding auth rules...");
        
        // 检查权限节点是否已存在
        $exists = Db::name('auth_rule')->where('name', 'banner')->find();
        if ($exists) {
            $output->writeln("Auth rules already exist, skipping...");
            return;
        }
        
        // 读取SQL文件
        $sqlFile = ROOT_PATH . 'application/database/seeds/BannerRule.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception("SQL file not found: " . $sqlFile);
        }
        
        $sql = file_get_contents($sqlFile);
        $sql = str_replace('fa_', config('database.prefix'), $sql);
        
        // 执行SQL
        Db::execute($sql);
        $output->writeln("Auth rules added successfully!");
    }
    
    /**
     * 添加示例数据
     */
    protected function addSampleData(Output $output)
    {
        $output->writeln("Adding sample data...");
        
        // 检查是否已有数据
        $count = Db::name('banner')->count();
        if ($count > 0) {
            $output->writeln("Sample data already exists, skipping...");
            return;
        }
        
        // 添加示例数据
        $time = time();
        $data = [
            [
                'title' => '轮播图1',
                'image' => '/assets/img/banner/banner1.jpg',
                'url' => '#',
                'weigh' => 10,
                'status' => 'normal',
                'createtime' => $time,
                'updatetime' => $time
            ],
            [
                'title' => '轮播图2',
                'image' => '/assets/img/banner/banner2.jpg',
                'url' => '#',
                'weigh' => 9,
                'status' => 'normal',
                'createtime' => $time,
                'updatetime' => $time
            ],
            [
                'title' => '轮播图3',
                'image' => '/assets/img/banner/banner3.jpg',
                'url' => '#',
                'weigh' => 8,
                'status' => 'normal',
                'createtime' => $time,
                'updatetime' => $time
            ]
        ];
        
        Db::name('banner')->insertAll($data);
        $output->writeln("Sample data added successfully!");
    }
}