<?php
/**
 * 测试ServiceItem控制器是否正常工作
 * 访问: http://your-domain/test_serviceitem.php
 */

// 引入ThinkPHP框架
require_once 'thinkphp/start.php';

use think\Db;
use app\common\model\ServiceItem;

try {
    echo "<h2>ServiceItem控制器测试</h2>";
    
    // 1. 测试数据库连接
    echo "<h3>1. 测试数据库连接</h3>";
    $count = Db::name('service_item')->count();
    echo "✅ 数据库连接正常，service_item表中有 {$count} 条记录<br>";
    
    // 2. 测试模型
    echo "<h3>2. 测试ServiceItem模型</h3>";
    $items = ServiceItem::getAvailableItems();
    echo "✅ ServiceItem模型正常，获取到 " . count($items) . " 个可用项目<br>";
    
    foreach ($items as $item) {
        echo "- ID:{$item->id} {$item->name} ¥{$item->current_price} ({$item->status})<br>";
    }
    
    // 3. 测试控制器类是否存在
    echo "<h3>3. 测试控制器类</h3>";
    if (class_exists('app\\admin\\controller\\Serviceitem')) {
        echo "✅ Serviceitem控制器类存在<br>";
    } else {
        echo "❌ Serviceitem控制器类不存在<br>";
    }
    
    // 4. 测试视图文件
    echo "<h3>4. 测试视图文件</h3>";
    $viewFiles = [
        'application/admin/view/serviceitem/index.html',
        'application/admin/view/serviceitem/edit.html'
    ];
    
    foreach ($viewFiles as $file) {
        if (file_exists($file)) {
            echo "✅ {$file} 存在<br>";
        } else {
            echo "❌ {$file} 不存在<br>";
        }
    }
    
    // 5. 测试JavaScript文件
    echo "<h3>5. 测试JavaScript文件</h3>";
    $jsFile = 'public/assets/js/backend/serviceitem.js';
    if (file_exists($jsFile)) {
        echo "✅ {$jsFile} 存在<br>";
    } else {
        echo "❌ {$jsFile} 不存在<br>";
    }
    
    // 6. 测试语言包
    echo "<h3>6. 测试语言包</h3>";
    $langFile = 'application/admin/lang/zh-cn/serviceitem.php';
    if (file_exists($langFile)) {
        echo "✅ {$langFile} 存在<br>";
        $langData = include $langFile;
        echo "语言包包含 " . count($langData) . " 个翻译项<br>";
    } else {
        echo "❌ {$langFile} 不存在<br>";
    }
    
    // 7. 检查菜单
    echo "<h3>7. 检查后台菜单</h3>";
    $menu = Db::name('auth_rule')->where('name', 'serviceitem')->find();
    if ($menu) {
        echo "✅ 主菜单存在，ID: {$menu['id']}, 标题: {$menu['title']}<br>";
        
        $subMenus = Db::name('auth_rule')->where('pid', $menu['id'])->select();
        echo "子菜单数量: " . count($subMenus) . "<br>";
        foreach ($subMenus as $sub) {
            echo "- {$sub['name']}: {$sub['title']}<br>";
        }
    } else {
        echo "❌ 主菜单不存在，需要手动添加<br>";
        echo "请执行 add_serviceitem_menu.sql 中的SQL语句<br>";
    }
    
    // 8. 测试URL访问
    echo "<h3>8. 测试URL访问</h3>";
    $baseUrl = 'http://' . $_SERVER['HTTP_HOST'];
    $adminUrl = $baseUrl . '/admin.php/serviceitem';
    echo "后台访问地址: <a href='{$adminUrl}' target='_blank'>{$adminUrl}</a><br>";
    echo "如果菜单已添加，可以直接访问上面的链接测试<br>";
    
    echo "<h3>✅ 测试完成</h3>";
    echo "如果所有项目都显示 ✅，说明ServiceItem后台管理功能已正确安装。<br>";
    echo "如果菜单不存在，请执行 add_serviceitem_menu.sql 中的SQL语句。<br>";
    
} catch (Exception $e) {
    echo "<h3>❌ 测试失败</h3>";
    echo "错误信息：" . $e->getMessage() . "<br>";
    echo "请检查数据库连接和文件权限。<br>";
}
