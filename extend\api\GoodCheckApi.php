<?php

namespace api;

/**
 * 精准查涉案接口类
 * 用于调用第三方涉案查询API
 */
class GoodCheckApi
{
    // API配置
    private $apiUrl = 'https://api.k-wz.com/api/goodcheckapi';
    private $appId;
    private $secret;
    
    /**
     * 构造函数
     * @param string $appId 平台申请的appid
     * @param string $secret 平台申请的密钥
     */
    public function __construct($appId, $secret)
    {
        $this->appId = $appId;
        $this->secret = $secret;
    }
    
    /**
     * 精准查涉案接口
     * @param array $params 查询参数
     * @return array 返回查询结果
     */
    public function goodCheck($params = [])
    {
        // 生成时间戳
        $timestamp = time();
        
        // 生成32位随机字符串
        $nonceStr = $this->generateNonceStr();
        
        // 默认参数
        $defaultParams = [
            'nonce_str' => $nonceStr,
            'datanumber' => '1',
            'detail' => '1',
            'serviceCode' => 'ALL',
            'classtype' => 'sf',
            'checktype' => '1',
            'querytype' => '0', // 0个人，1企业
        ];
        
        // 合并参数
        $requestParams = array_merge($defaultParams, $params);
        
        // 生成签名
        $sign = $this->generateSign($requestParams, $timestamp);
        
        // 构建请求头
        $headers = [
            'Content-Type: application/json',
            'X-Auth-Key: ' . $this->appId,
            'X-Auth-Sign: ' . $sign,
            'X-Auth-Timestamp: ' . $timestamp,
        ];
        // 发送请求
        return $this->sendRequest($requestParams, $headers);
    }
    
    /**
     * 个人涉案查询
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param string $orderno 订单号（可选）
     * @param string $clientIp 查询IP（可选）
     * @param string $interfaceCoding 自定义查询编码（可选）
     * @return array
     */
    public function checkPerson($name, $idCard, $orderno = '', $clientIp = '', $interfaceCoding = '')
    {
        $params = [
            'name' => [$name],
            'code' => $idCard,
            'querytype' => '0', // 个人查询
        ];
        
        if (!empty($orderno)) {
            $params['orderno'] = $orderno;
        }
        
        if (!empty($clientIp)) {
            $params['client_ip'] = $clientIp;
        }
        
        if (!empty($interfaceCoding)) {
            $params['interfacecoding'] = $interfaceCoding;
        }
        
        return $this->goodCheck($params);
    }
    
    /**
     * 企业涉案查询
     * @param string $companyName 企业名称
     * @param string $taxNumber 企业税号
     * @param string $orderno 订单号（可选）
     * @param string $clientIp 查询IP（可选）
     * @param string $interfaceCoding 自定义查询编码（可选）
     * @return array
     */
    public function checkCompany($companyName, $taxNumber, $orderno = '', $clientIp = '', $interfaceCoding = '')
    {
        $params = [
            'name' => [$companyName],
            'code' => $taxNumber,
            'querytype' => '1', // 企业查询
        ];
        
        if (!empty($orderno)) {
            $params['orderno'] = $orderno;
        }
        
        if (!empty($clientIp)) {
            $params['client_ip'] = $clientIp;
        }
        
        if (!empty($interfaceCoding)) {
            $params['interfacecoding'] = $interfaceCoding;
        }
        
        return $this->goodCheck($params);
    }
    
    /**
     * 生成签名
     * @param array $params 参数数组
     * @param int $timestamp 时间戳
     * @return string 签名
     */
    private function generateSign($params, $timestamp)
    {
        // 添加appid到参数中用于签名
        $signParams = $params;
        //$signParams['appid'] = $this->appId;
        
        // 移除空值参数
        $signParams = array_filter($signParams, function($value) {
            return $value !== '' && $value !== null;
        });
        
        // 按参数名ASCII码从小到大排序
        ksort($signParams);
        
        // 拼接成字符串
        $stringA = '';
        foreach ($signParams as $key => $value) {
            if (is_array($value)) {
                $value = implode(',', $value);
            }
            $stringA .= $key . '=' . $value . '&';
        }
        $stringA = rtrim($stringA, '&');
        
        // 拼接时间戳和密钥
        $stringSignTemp = $stringA . '&timestamp=' . $timestamp . '&secret=' . $this->secret;
        // MD5加密
        return md5($stringSignTemp);
    }
    
    /**
     * 生成32位随机字符串
     * @return string
     */
    private function generateNonceStr()
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $str = '';
        for ($i = 0; $i < 32; $i++) {
            $str .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $str;
    }
    
    /**
     * 发送HTTP请求
     * @param array $params 请求参数
     * @param array $headers 请求头
     * @return array
     */
    private function sendRequest($params, $headers)
    {
        // 将参数转换为get格式
        $jsonData = json_encode($params, JSON_UNESCAPED_UNICODE);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'message' => 'CURL错误: ' . $error,
                'data' => null
            ];
        }
        
        if ($httpCode !== 200) {
            return [
                'success' => false,
                'message' => 'HTTP错误: ' . $httpCode,
                'data' => null
            ];
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => 'JSON解析错误: ' . json_last_error_msg(),
                'data' => $response
            ];
        }
        
        return [
            'success' => true,
            'message' => '请求成功',
            'data' => $result
        ];
    }
}
