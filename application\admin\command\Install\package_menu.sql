-- 添加套餐管理菜单
INSERT INTO `fa_auth_rule` (`id`, `type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(NULL, 'file', 0, 'package', '套餐管理', 'fa fa-cube', '', '套餐管理', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
(NULL, 'file', (SELECT id FROM (SELECT id FROM `fa_auth_rule` WHERE name = 'package') AS temp), 'package/index', '查看', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
(NULL, 'file', (SELECT id FROM (SELECT id FROM `fa_auth_rule` WHERE name = 'package') AS temp), 'package/add', '添加', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
(NULL, 'file', (SELECT id FROM (SELECT id FROM `fa_auth_rule` WHERE name = 'package') AS temp), 'package/edit', '编辑', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
(NULL, 'file', (SELECT id FROM (SELECT id FROM `fa_auth_rule` WHERE name = 'package') AS temp), 'package/del', '删除', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
(NULL, 'file', (SELECT id FROM (SELECT id FROM `fa_auth_rule` WHERE name = 'package') AS temp), 'package/multi', '批量更新', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');
