<?php
/**
 * 聘查查API使用示例（新版本）
 * 支持 app_id 和 secret 配置
 */

require_once '../PinccApi.php';
require_once '../config/ApifoxConfig.php';
require_once '../ApifoxFactory.php';

use apifox\PinccApi;
use apifox\ApifoxFactory;
use apifox\config\ApifoxConfig;

echo "=== 聘查查API使用示例（新版本） ===\n\n";

// 1. 检查配置
echo "1. 检查API配置...\n";
$validation = ApifoxFactory::validateConfig();
if (!$validation['valid']) {
    echo "❌ 配置验证失败：\n";
    foreach ($validation['errors'] as $error) {
        echo "   - " . $error . "\n";
    }
    echo "\n请先配置正确的 app_id 和 secret 后再运行示例\n";
    echo "编辑文件：extend/apifox/config/ApifoxConfig.php\n";
    exit;
}
echo "✅ 配置验证通过\n\n";

// 2. 显示配置信息
echo "2. 当前配置信息：\n";
$configInfo = ApifoxFactory::getConfigInfo();
foreach ($configInfo as $key => $value) {
    echo "   {$key}: " . (is_bool($value) ? ($value ? '是' : '否') : $value) . "\n";
}
echo "\n";

// 3. 使用工厂类进行快速查询（推荐方式）
echo "3. 使用工厂类进行快速查询：\n";

try {
    // 婚姻查询
    echo "   - 婚姻查询测试...\n";
    $result = ApifoxFactory::quickMarriageQuery('张三', '420101199001010001');
    if ($result['success']) {
        echo "     ✅ 婚姻查询成功\n";
        if (isset($result['data']['data']['resultCode'])) {
            $resultCode = $result['data']['data']['resultCode'];
            echo "     结果代码: {$resultCode}\n";
        }
    } else {
        echo "     ❌ 婚姻查询失败: " . $result['message'] . "\n";
    }

    // 学历查询（星耀版）
    echo "   - 学历查询（星耀版）测试...\n";
    $result = ApifoxFactory::quickEducationQueryV6('李四', '******************');
    if ($result['success']) {
        echo "     ✅ 学历查询成功\n";
    } else {
        echo "     ❌ 学历查询失败: " . $result['message'] . "\n";
    }

    // 个人失信查询
    echo "   - 个人失信查询测试...\n";
    $result = ApifoxFactory::quickPersonCreditQuery('王五', '******************');
    if ($result['success']) {
        echo "     ✅ 个人失信查询成功\n";
    } else {
        echo "     ❌ 个人失信查询失败: " . $result['message'] . "\n";
    }

} catch (Exception $e) {
    echo "❌ API调用异常: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. 直接使用API类（高级用法）
echo "4. 直接使用API类（高级用法）：\n";

try {
    // 使用配置文件中的密钥创建API实例
    $api = new PinccApi();
    
    // 显示API配置信息
    $apiConfig = $api->getConfig();
    echo "   API配置信息：\n";
    foreach ($apiConfig as $key => $value) {
        echo "     {$key}: " . (is_bool($value) ? ($value ? '是' : '否') : $value) . "\n";
    }
    
    // 进行查询
    echo "   - 综合资质评分查询测试...\n";
    $result = $api->qualifyScore('赵六', '******************', '13800138000');
    if ($result['success']) {
        echo "     ✅ 综合资质评分查询成功\n";
    } else {
        echo "     ❌ 综合资质评分查询失败: " . $result['message'] . "\n";
    }

} catch (Exception $e) {
    echo "❌ API实例创建失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. 环境变量配置示例
echo "5. 环境变量配置说明：\n";
echo "   为了安全起见，建议使用环境变量配置敏感信息：\n";
echo "   \n";
echo "   在 .env 文件中设置：\n";
echo "   PINCC_APP_ID=your_actual_app_id\n";
echo "   PINCC_SECRET=your_actual_secret_key\n";
echo "   PINCC_DEBUG=false\n";
echo "   \n";
echo "   或在系统环境变量中设置：\n";
echo "   export PINCC_APP_ID=your_actual_app_id\n";
echo "   export PINCC_SECRET=your_actual_secret_key\n";
echo "\n";

// 6. 错误处理示例
echo "6. 错误处理示例：\n";
$result = ApifoxFactory::quickMarriageQuery('', ''); // 传入空参数测试错误处理

if ($result['success']) {
    echo "   查询成功（不应该出现）\n";
} else {
    echo "   ✅ 正确捕获错误: " . $result['message'] . "\n";
}

echo "\n=== 示例完成 ===\n";

/**
 * 在ThinkPHP控制器中的使用示例：
 * 
 * <?php
 * namespace app\api\controller;
 * 
 * use think\Controller;
 * use apifox\ApifoxFactory;
 * 
 * class PersonCheck extends Controller
 * {
 *     // 婚姻状况查询
 *     public function marriageCheck()
 *     {
 *         $name = $this->request->param('name');
 *         $cardNo = $this->request->param('card_no');
 *         
 *         if (empty($name) || empty($cardNo)) {
 *             return json(['code' => 400, 'msg' => '参数不能为空']);
 *         }
 *         
 *         try {
 *             $result = ApifoxFactory::quickMarriageQuery($name, $cardNo);
 *             
 *             if ($result['success']) {
 *                 return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
 *             } else {
 *                 return json(['code' => 500, 'msg' => $result['message']]);
 *             }
 *         } catch (Exception $e) {
 *             return json(['code' => 500, 'msg' => 'API配置错误: ' . $e->getMessage()]);
 *         }
 *     }
 * }
 */
