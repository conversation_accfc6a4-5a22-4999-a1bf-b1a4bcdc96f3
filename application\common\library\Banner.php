<?php

namespace app\common\library;

use app\common\model\Banner as BannerModel;

/**
 * 轮播图辅助类
 */
class Banner
{
    /**
     * 获取轮播图列表
     * 
     * @param int $limit 限制数量，默认为5
     * @param string $status 状态，默认为normal
     * @return array
     */
    public static function getBannerList($limit = 5, $status = 'normal')
    {
        $bannerList = BannerModel::where('status', $status)
            ->order('weigh', 'desc')
            ->limit($limit)
            ->select();
        
        return collection($bannerList)->toArray();
    }
    
    /**
     * 获取单个轮播图
     * 
     * @param int $id 轮播图ID
     * @return array|null
     */
    public static function getBanner($id)
    {
        $banner = BannerModel::where('status', 'normal')
            ->find($id);
        
        return $banner ? $banner->toArray() : null;
    }
    
    /**
     * 渲染轮播图组件
     * 
     * @param int $limit 限制数量，默认为5
     * @return string
     */
    public static function render($limit = 5)
    {
        $bannerList = self::getBannerList($limit);
        $view = new \think\View();
        $view->assign('bannerList', $bannerList);
        return $view->fetch('common/banner');
    }
}