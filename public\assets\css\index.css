html,
body {
    height: 100%;
    width: 100%;
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: "Roboto", "SF Pro SC", "SF Pro Display", "SF Pro Icons", "PingFang SC", BlinkMacSystemFont, -apple-system, "Segoe UI", "Microsoft Yahei", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    font-weight: 400;
    margin: 0;
}

a {
    -webkit-transition: all 0.35s;
    -moz-transition: all 0.35s;
    transition: all 0.35s;
    color: #474157;
}

a:hover,
a:focus {
    color: #474157;
}

hr {
    max-width: 100px;
    margin: 25px auto 0;
    border-width: 1px;
    border-color: rgba(34, 34, 34, 0.1);
}

hr.light {
    border-color: white;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 200;
    letter-spacing: 1px;
}

p {
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 20px;
}

    /* 轮播图样式优化 */
    .carousel {
        touch-action: pan-y pinch-zoom;
    }
    .carousel .item {
        height: auto; /* 自动高度，适应内容 */
        min-height: auto;
    }
    .carousel .item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .carousel-indicators {
        bottom: 10px;
        margin-bottom: 0;
    }
    .carousel-indicators li {
        margin: 0 3px;
        border-color: rgba(255,255,255,0.7);
        width: 8px;
        height: 8px;
        border-radius: 50%;
    }
    .carousel-indicators .active {
        margin: 0 3px;
        background-color: #fff;
    }

    /* 触摸反馈 */
    .carousel .item a {
        display: block;
        width: 100%;
        height: 100%;
        position: relative;
        -webkit-tap-highlight-color: rgba(0,0,0,0);
    }
    .carousel .item a:active {
        background-color: transparent;
    }

    /* 重置默认样式，消除空白 */
    body {
        margin: 0;
        padding: 0;
    }

    /* 隐藏原有的mainbody */
    #mainbody {
        display: none;
    }

    /* 新的移动端风格样式 */
    .mobile-app-container {
        background: linear-gradient(180deg, #f8fbff 0%, #ffffff 100%);
        padding: 20px 15px;
        padding-bottom: 80px; /* 为底部导航留出空间 */
    }

    .app-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 0 5px;
    }

    .app-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }

    .app-subtitle {
        background: #4285f4;
        color: white;
        padding: 4px 12px;
        border-radius: 12px 3px;
        font-size: 12px;
        margin-left: 10px;
    }

    .status-icons {
        display: flex;
        gap: 5px;
        font-size: 14px;
        color: #666;
    }

    /* 轮播卡片 */
    .main-banner-card {
        background: #fff;
        border-radius: 16px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(235, 235, 235, 0.3);
        min-height: 185px;
        height: 185px;
    }

    .banner-illustration {
        width: 100%;
        height: 100%;
        position: relative;
    }

    .banner-illustration img {
        width: 100%;
        height: 100%;
        border-radius: 16px;
        object-fit: cover;
        border: none;
        outline: none;
    }

    /* 卡片 */
    .couple-report-card {
        background: white;
        border-radius: 16px;
        padding: 10px 20px;
        margin: 20px 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .report-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .report-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-top: 0;
    }

    .report-price {
        text-align: right;
    }

    .current-price {
        font-size: 20px;
        font-weight: bold;
        color: #ff4757;
    }

    .original-price {
        font-size: 14px;
        color: #999;
        text-decoration: line-through;
        position: relative;
        bottom: 5px;
    }

    .report-options {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
    }

    .option-item {
        text-align: center;
        padding: 10px 8px;
        border-radius: 12px;
        background: #f8f9fa;
    }

    .option-item.pink {
        background: linear-gradient(178deg, #fce4ec 0%, #ffffff 100%);
    }

    .option-item.purple {
        background: linear-gradient(178deg, #ce93d8 0%, #ffffff 100%);
    }

    .option-item.blue {
        background: linear-gradient(178deg, #90caf9 0%, #ffffff 100%);
    }

    .option-item.green {
        background: linear-gradient(178deg, #a5d6a7 0%, #ffffff 100%);
    }

    .option-icon {
        width: 32px;
        height: 32px;
        margin: 0 auto 5px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
    }

    .option-icon img {
        width: 30px;
        height: 30px;
    }

    .option-text {
        font-size: 12px;
        color: #333;
        font-weight: 500;
    }

    .more-info {
        text-align: center;
        color: #999;
        font-size: 12px;
        margin-top: 10px;
    }

    /* 选取模块区域 */
    .module-selection {
        background: white;
        border-radius: 16px;
        padding: 10px;
        margin: 20px 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .module-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .module-icon {
        width: 24px;
        height: 24px;
        background: #ff4757;
        border-radius: 50%;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
    }

    .module-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-top: 10px;
    }

    .module-item {
        border: 1px solid #eee;
        border-radius: 5px;
        padding: 10px 15px;
        margin-bottom: 10px;
    }

    .module-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .module-checkbox {
        display: flex;
        align-items: center;
        position: relative;
        bottom: 5px;
    }

    .checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 4px;
        margin-right: 8px;
        position: relative;
        cursor: pointer;
    }

    .checkbox.checked {
        background: #ff4757;
        border-color: #ff4757;
    }

    .checkbox.checked::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .module-name {
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .module-price {
        text-align: right;
    }

    .module-current-price {
        font-size: 18px;
        font-weight: bold;
        color: #ff4757;
    }

    .module-original-price {
        font-size: 12px;
        color: #999;
        text-decoration: line-through;
        position: relative;
        bottom: 5px;
    }

    .module-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
    }

    .module-tag {
        background: #fff0f0;
        color: #ff4757;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 11px;
        border: 1px solid #ffe0e0;
    }

    /* 基本信息区域 */
    .basic-info {
        background: white;
        border-radius: 16px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .info-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .info-link {
        color: #5dade2;
        font-size: 14px;
        text-decoration: none;
    }

    .info-subtitle {
        color: #666;
        font-size: 14px;
        margin-bottom: 20px;
    }

    /* 单项/套餐切换菜单 */
    .tab-menu {
        display: flex;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 4px;
        margin-bottom: 20px;
    }

    .tab-item {
        flex: 1;
        text-align: center;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .tab-item.active {
        background: white;
        color: #ff4757;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 内容区域 */
    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    /* 单项选择样式 */
    .single-item {
        border: 1px solid #eee;
        border-radius: 5px;
        padding: 10px 15px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .single-item:hover {
        border-color: #ff4757;
        background: #fff8f8;
    }

    .single-item.selected {
        border-color: #ff4757;
        background: #fff0f0;
    }

    .single-item-left {
        display: flex;
        align-items: center;
    }

    .single-item-checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 4px;
        margin-right: 12px;
        position: relative;
        transition: all 0.3s ease;
    }

    .single-item.selected .single-item-checkbox {
        background: #ff4757;
        border-color: #ff4757;
    }

    .single-item.selected .single-item-checkbox::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .single-item-info h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }

    .single-item-info p {
        margin: 0;
        font-size: 12px;
        color: #999;
    }

    .single-item-price {
        text-align: right;
    }

    .single-item-current {
        font-size: 16px;
        font-weight: bold;
        color: #ff4757;
    }

    .single-item-original {
        font-size: 12px;
        color: #999;
        text-decoration: line-through;
    }

    /* 表单输入样式 */
    .form-group {
        margin-bottom: 10px;
    }

    .form-input {
        width: 100%;
        padding: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        font-size: 16px;
        background: #f8f9fa;
        color: #333;
        box-sizing: border-box;
        transition: all 0.3s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: #ff4757;
        background: white;
        box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
    }

    .form-input::placeholder {
        color: #999;
        font-size: 16px;
    }

    /* 图标输入框 */
    .input-with-icon {
        position: relative;
    }

    .input-icon {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        font-size: 18px;
        pointer-events: none;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .input-icon svg {
        width: 20px;
        height: 20px;
        fill: #999;
    }

    .input-with-icon .form-input {
        padding-left: 50px;
    }

    /* 协议同意 */
    .agreement-section {
        margin: 20px 0;
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }

    .agreement-checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 4px;
        position: relative;
        cursor: pointer;
        flex-shrink: 0;
        margin-top: 2px;
        transition: all 0.3s ease;
    }

    .agreement-checkbox.checked {
        background: #ff4757;
        border-color: #ff4757;
    }

    .agreement-checkbox.checked::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .agreement-text {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
    }

    .agreement-link {
        color: #5dade2;
        text-decoration: none;
    }

    .agreement-link:hover {
        text-decoration: underline;
    }

    /* 查询按钮 */
    .query-button {
        width: 100%;
        padding: 10px;
        background: #bbb;
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: bold;
        cursor: not-allowed;
        transition: all 0.3s ease;
        position: relative;
    }

    .query-button.active {
        background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
    }

    .query-button.active:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
    }

    .query-button:active {
        transform: translateY(0);
    }

    .query-button span {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 12px;
        font-weight: 500;
        background: hsl(0deg 0% 100% / 39%);
        padding: 2px 5px;
        border-radius: 5px 0;
    }

    /* 底部导航 */
    .bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-around;
        padding: 8px 0;
        z-index: 1000;
    }

    .nav-item {
        text-align: center;
        color: #999;
        text-decoration: none;
        font-size: 12px;
        flex: 1;
    }

    .nav-item.active {
        color: #4285f4;
    }

    .nav-icon {
        width: 24px;
        height: 24px;
        margin: 0 auto 4px;
        background-size: contain;
    }

    /* 响应式调整 */
    @media (max-width: 480px) {
        .mobile-app-container {
            padding: 15px 10px;
            padding-bottom: 80px;
        }

        /* 小屏幕下保持铺满效果 */
    }
    .hui-s {
        color: #999999;
        font-size: 13px;
    }