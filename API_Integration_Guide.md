# API联合查询集成指南

## 🎯 概述

本系统集成了两套API接口，实现多维度的个人信息查询：

1. **精准查涉案API**：查询个人涉案记录
2. **聘查查API（Apifox）**：查询婚姻、学历、失信等信息

## 🚀 功能特点

### ✅ 支持的查询类型
- **涉案记录查询**：民事、刑事、执行案件
- **婚姻状况查询**：全国婚姻登记信息
- **学历信息查询**：教育背景验证
- **个人失信查询**：失信被执行人记录

### ⚡ 查询策略
- **同步查询**：适合1-3个API，用户等待结果
- **并行查询**：适合4+个API，显著提升查询速度
- **智能选择**：根据查询类型自动选择最优策略

## 📋 使用方法

### 1. 基础查询
```
GET /index/query?name=张三&id_card=******************
```

### 2. 指定查询类型
```
GET /index/query?name=张三&id_card=******************&query_type=basic
```

**查询类型说明：**
- `basic`：基础查询（婚姻+失信）
- `detail`：详细查询（婚姻+学历+失信）
- `all`：全面查询（所有API，默认）

### 3. 指定查询策略
```
GET /index/query?name=张三&id_card=******************&parallel=sync
```

**查询策略说明：**
- `auto`：自动选择（默认）
- `sync`：同步查询
- `parallel`：并行查询

### 4. AJAX查询
```javascript
$.ajax({
    url: '/index/query',
    type: 'GET',
    data: {
        name: '张三',
        id_card: '******************',
        query_type: 'all',
        parallel: 'auto'
    },
    dataType: 'json',
    success: function(response) {
        if (response.success) {
            console.log('查询成功', response.results);
            console.log('风险摘要', response.summary);
        } else {
            console.log('查询失败', response.message);
        }
    }
});
```

## 📊 返回数据结构

### 成功响应
```json
{
    "success": true,
    "message": "联合查询完成",
    "query_info": {
        "name": "张三",
        "id_card": "420101****0001",
        "query_time": "2025-06-23 10:30:00",
        "total_time": "3.45秒"
    },
    "results": {
        "goodcheck": {
            "api_name": "精准查涉案",
            "success": true,
            "data": { /* 涉案查询结果 */ },
            "message": "查询成功",
            "query_time": "2.1秒"
        },
        "pincc": {
            "total_apis": 3,
            "total_time": "1.35秒",
            "results": {
                "marriage": {
                    "api_name": "婚姻状况查询",
                    "success": true,
                    "data": { /* 婚姻查询结果 */ },
                    "message": "查询成功",
                    "query_time": "0.45秒"
                },
                "education": {
                    "api_name": "学历信息查询",
                    "success": true,
                    "data": { /* 学历查询结果 */ },
                    "message": "查询成功",
                    "query_time": "0.5秒"
                },
                "credit": {
                    "api_name": "个人失信查询",
                    "success": true,
                    "data": { /* 失信查询结果 */ },
                    "message": "查询成功",
                    "query_time": "0.4秒"
                }
            }
        }
    },
    "summary": {
        "total_apis": 4,
        "success_count": 4,
        "failed_count": 0,
        "risk_level": "medium",
        "risk_factors": [
            "涉案记录：2件案件",
            "婚姻状况：已婚"
        ],
        "recommendations": [
            "存在一定风险，建议加强监控",
            "可考虑适当的风控措施"
        ]
    }
}
```

### 失败响应
```json
{
    "success": false,
    "message": "查询失败：身份证号格式错误",
    "error_code": 400
}
```

## 🎯 风险等级说明

### 风险等级
- **low**：风险较低，可正常合作
- **medium**：存在一定风险，建议加强监控
- **high**：风险较高，建议谨慎合作

### 风险因素示例
- 涉案记录：X件案件
- 未结案件：X件
- 涉案金额：X万元
- 存在失信记录
- 婚姻状况：已婚/未婚/离异/丧偶

## ⚡ 性能优化

### 查询策略选择
| 场景 | API数量 | 推荐策略 | 预计耗时 |
|------|---------|----------|----------|
| 快速验证 | 1-2个 | 同步查询 | 1-3秒 |
| 基础背调 | 2-3个 | 同步查询 | 3-6秒 |
| 全面背调 | 4+个 | 并行查询 | 3-5秒 |

### 并行查询优势
- ✅ **时间节省**：4个API并行执行仅需3-5秒
- ✅ **用户体验**：显著减少等待时间
- ✅ **资源利用**：充分利用网络带宽

## 🔧 配置说明

### 1. API密钥配置
在 `application/extra/site.php` 中配置：
```php
'jc_appid' => 'your_goodcheck_app_id',
'jc_secret' => 'your_goodcheck_secret',
```

### 2. 聘查查配置
在 `extend/apifox/config/ApifoxConfig.php` 中配置：
```php
const PINCC_API = [
    'app_id' => 'your_app_id',
    'secret' => 'your_secret_key',
    // ...
];
```

## ⚠️ 注意事项

### 1. 数据安全
- 身份证号自动脱敏显示
- 查询日志记录（建议）
- 敏感数据加密存储

### 2. 错误处理
- 单个API失败不影响其他API
- 详细的错误信息返回
- 自动重试机制（建议）

### 3. 频率限制
- 注意各API的调用频率限制
- 建议添加查询间隔控制
- 监控API调用量

## 🎨 前端集成示例

### HTML表单
```html
<form id="queryForm">
    <input type="text" name="name" placeholder="姓名" required>
    <input type="text" name="id_card" placeholder="身份证号" required>
    <select name="query_type">
        <option value="basic">基础查询</option>
        <option value="detail">详细查询</option>
        <option value="all">全面查询</option>
    </select>
    <button type="submit">查询</button>
</form>
```

### JavaScript处理
```javascript
$('#queryForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = $(this).serialize();
    
    // 显示加载状态
    showLoading();
    
    $.ajax({
        url: '/index/query',
        type: 'GET',
        data: formData,
        dataType: 'json',
        success: function(response) {
            hideLoading();
            
            if (response.success) {
                displayResults(response);
                displayRiskSummary(response.summary);
            } else {
                showError(response.message);
            }
        },
        error: function() {
            hideLoading();
            showError('查询请求失败，请稍后重试');
        }
    });
});
```

## 📈 监控建议

### 1. 性能监控
- API响应时间
- 成功率统计
- 并发查询数量

### 2. 业务监控
- 查询量统计
- 风险等级分布
- 用户行为分析

### 3. 告警设置
- API调用失败率超过阈值
- 响应时间超过预期
- 异常查询模式

这个集成方案提供了灵活、高效、安全的多API查询能力，可以根据实际业务需求进行调整和优化。
