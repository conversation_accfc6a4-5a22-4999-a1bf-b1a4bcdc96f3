<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Cache;
use think\Db;

/**
 * 队列管理命令
 */
class QueueManager extends Command
{
    protected function configure()
    {
        $this->setName('queue:manager')
            ->setDescription('队列管理工具')
            ->addArgument('action', null, '操作类型：status|clean|monitor|restart');
    }

    protected function execute(Input $input, Output $output)
    {
        $action = $input->getArgument('action');
        
        switch ($action) {
            case 'status':
                $this->showStatus($output);
                break;
            case 'clean':
                $this->cleanExpiredTasks($output);
                break;
            case 'monitor':
                $this->monitorQueues($output);
                break;
            case 'restart':
                $this->restartWorkers($output);
                break;
            default:
                $this->showHelp($output);
        }
    }
    
    /**
     * 显示队列状态
     */
    private function showStatus(Output $output)
    {
        $output->writeln('<info>队列状态统计</info>');
        $output->writeln('==================');
        
        try {
            // 统计Redis中的队列信息
            $redis = new \Redis();
            $redis->connect('127.0.0.1', 6379);
            
            $queues = ['multi_api_query', 'batch_query', 'default'];
            
            foreach ($queues as $queue) {
                $length = $redis->lLen("queues:{$queue}");
                $output->writeln("队列 {$queue}: {$length} 个待处理任务");
            }
            
            // 统计缓存中的任务
            $this->showCacheStats($output);
            
            $redis->close();
            
        } catch (\Exception $e) {
            $output->writeln('<error>获取队列状态失败: ' . $e->getMessage() . '</error>');
        }
    }
    
    /**
     * 显示缓存统计
     */
    private function showCacheStats(Output $output)
    {
        $output->writeln('');
        $output->writeln('<info>缓存任务统计</info>');
        $output->writeln('==================');
        
        try {
            $redis = new \Redis();
            $redis->connect('127.0.0.1', 6379);
            
            // 统计不同类型的缓存
            $patterns = [
                'queue_task_*' => '队列任务',
                'queue_result_*' => '任务结果',
                'batch_task_*' => '批量任务'
            ];
            
            foreach ($patterns as $pattern => $name) {
                $keys = $redis->keys($pattern);
                $count = count($keys);
                $output->writeln("{$name}: {$count} 个");
            }
            
            $redis->close();
            
        } catch (\Exception $e) {
            $output->writeln('<error>获取缓存统计失败: ' . $e->getMessage() . '</error>');
        }
    }
    
    /**
     * 清理过期任务
     */
    private function cleanExpiredTasks(Output $output)
    {
        $output->writeln('<info>开始清理过期任务...</info>');
        
        try {
            $redis = new \Redis();
            $redis->connect('127.0.0.1', 6379);
            
            $cleanedCount = 0;
            $patterns = ['queue_task_*', 'queue_result_*', 'batch_task_*'];
            
            foreach ($patterns as $pattern) {
                $keys = $redis->keys($pattern);
                
                foreach ($keys as $key) {
                    $ttl = $redis->ttl($key);
                    
                    // 如果TTL为-1（永不过期）且创建时间超过24小时，则删除
                    if ($ttl === -1) {
                        $data = $redis->get($key);
                        if ($data) {
                            $taskData = json_decode($data, true);
                            if (isset($taskData['created_at'])) {
                                $createdTime = strtotime($taskData['created_at']);
                                if (time() - $createdTime > 86400) { // 24小时
                                    $redis->del($key);
                                    $cleanedCount++;
                                }
                            }
                        }
                    }
                }
            }
            
            $output->writeln("<info>清理完成，共清理 {$cleanedCount} 个过期任务</info>");
            
            $redis->close();
            
        } catch (\Exception $e) {
            $output->writeln('<error>清理过期任务失败: ' . $e->getMessage() . '</error>');
        }
    }
    
    /**
     * 监控队列
     */
    private function monitorQueues(Output $output)
    {
        $output->writeln('<info>开始监控队列（按Ctrl+C退出）...</info>');
        
        while (true) {
            try {
                $redis = new \Redis();
                $redis->connect('127.0.0.1', 6379);
                
                $output->writeln('');
                $output->writeln('[' . date('Y-m-d H:i:s') . '] 队列状态:');
                
                $queues = ['multi_api_query', 'batch_query', 'default'];
                $totalTasks = 0;
                
                foreach ($queues as $queue) {
                    $length = $redis->lLen("queues:{$queue}");
                    $totalTasks += $length;
                    
                    $status = $length > 100 ? '<error>高负载</error>' : 
                             ($length > 50 ? '<comment>中负载</comment>' : '<info>正常</info>');
                    
                    $output->writeln("  {$queue}: {$length} 个任务 {$status}");
                }
                
                if ($totalTasks > 200) {
                    $output->writeln('<error>警告：队列任务过多，建议增加worker数量！</error>');
                }
                
                $redis->close();
                
                sleep(10); // 每10秒检查一次
                
            } catch (\Exception $e) {
                $output->writeln('<error>监控异常: ' . $e->getMessage() . '</error>');
                sleep(5);
            }
        }
    }
    
    /**
     * 重启Workers
     */
    private function restartWorkers(Output $output)
    {
        $output->writeln('<info>重启队列Workers...</info>');
        
        try {
            // 发送重启信号
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows系统
                exec('taskkill /F /IM php.exe');
                $output->writeln('已终止现有PHP进程');
                
                // 重新启动workers
                $commands = [
                    'start /B php think queue:work --queue=multi_api_query',
                    'start /B php think queue:work --queue=batch_query',
                    'start /B php think queue:work --queue=default'
                ];
                
                foreach ($commands as $cmd) {
                    pclose(popen($cmd, "r"));
                }
                
            } else {
                // Linux/Unix系统
                exec('pkill -f "queue:work"');
                $output->writeln('已终止现有队列进程');
                
                // 重新启动workers
                $commands = [
                    'nohup php think queue:work --queue=multi_api_query > /dev/null 2>&1 &',
                    'nohup php think queue:work --queue=batch_query > /dev/null 2>&1 &',
                    'nohup php think queue:work --queue=default > /dev/null 2>&1 &'
                ];
                
                foreach ($commands as $cmd) {
                    exec($cmd);
                }
            }
            
            $output->writeln('<info>队列Workers重启完成</info>');
            
        } catch (\Exception $e) {
            $output->writeln('<error>重启Workers失败: ' . $e->getMessage() . '</error>');
        }
    }
    
    /**
     * 显示帮助信息
     */
    private function showHelp(Output $output)
    {
        $output->writeln('<info>队列管理工具使用说明</info>');
        $output->writeln('========================');
        $output->writeln('');
        $output->writeln('<comment>可用命令：</comment>');
        $output->writeln('  php think queue:manager status   - 显示队列状态');
        $output->writeln('  php think queue:manager clean    - 清理过期任务');
        $output->writeln('  php think queue:manager monitor  - 实时监控队列');
        $output->writeln('  php think queue:manager restart  - 重启Workers');
        $output->writeln('');
        $output->writeln('<comment>队列Worker管理：</comment>');
        $output->writeln('  php think queue:work              - 启动默认队列Worker');
        $output->writeln('  php think queue:work --queue=multi_api_query  - 启动多API查询Worker');
        $output->writeln('  php think queue:work --queue=batch_query      - 启动批量查询Worker');
        $output->writeln('');
        $output->writeln('<comment>示例：</comment>');
        $output->writeln('  # 查看队列状态');
        $output->writeln('  php think queue:manager status');
        $output->writeln('');
        $output->writeln('  # 启动监控（实时显示队列状态）');
        $output->writeln('  php think queue:manager monitor');
        $output->writeln('');
        $output->writeln('  # 清理过期任务（建议定期执行）');
        $output->writeln('  php think queue:manager clean');
    }
}
