<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Package as PackageModel;

/**
 * 套餐接口
 */
class Package extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    
    /**
     * 获取套餐列表
     *
     * @ApiTitle    (获取套餐列表)
     * @ApiSummary  (获取所有可见的套餐)
     * @ApiMethod   (GET)
     * @ApiParams   (name="type", type="string", required=false, description="套餐类型:single=单项,package=套餐")
     * @ApiReturn   ({"code":1,"msg":"","data":{"single":[...],"package":[...]}})
     */
    public function index()
    {
        $type = $this->request->get('type', '');
        
        if ($type) {
            // 获取指定类型的套餐
            $list = PackageModel::getNormalList($type);
        } else {
            // 获取所有套餐，按类型分组
            $singleList = PackageModel::getSingleList();
            $packageList = PackageModel::getPackageList();
            
            $list = [
                'single' => $singleList,
                'package' => $packageList
            ];
        }
        
        $this->success('获取成功', $list);
    }
    
    /**
     * 获取单项列表
     *
     * @ApiTitle    (获取单项列表)
     * @ApiSummary  (获取所有单项套餐)
     * @ApiMethod   (GET)
     * @ApiReturn   ({"code":1,"msg":"","data":[...]})
     */
    public function single()
    {
        $list = PackageModel::getSingleList();
        $this->success('获取成功', $list);
    }
    
    /**
     * 获取套餐列表
     *
     * @ApiTitle    (获取套餐列表)
     * @ApiSummary  (获取所有套餐)
     * @ApiMethod   (GET)
     * @ApiReturn   ({"code":1,"msg":"","data":[...]})
     */
    public function package()
    {
        $list = PackageModel::getPackageList();
        $this->success('获取成功', $list);
    }
    
    /**
     * 获取套餐详情
     *
     * @ApiTitle    (获取套餐详情)
     * @ApiSummary  (根据ID获取套餐详情)
     * @ApiMethod   (GET)
     * @ApiParams   (name="id", type="integer", required=true, description="套餐ID")
     * @ApiReturn   ({"code":1,"msg":"","data":{...}})
     */
    public function detail()
    {
        $id = $this->request->get('id/d', 0);
        
        if (!$id) {
            $this->error('参数错误');
        }
        
        $package = PackageModel::where('id', $id)
            ->where('status', 'normal')
            ->find();
            
        if (!$package) {
            $this->error('套餐不存在');
        }
        
        $this->success('获取成功', $package);
    }
}
