# 轮播图模块使用说明

## 功能介绍
轮播图模块提供了一个完整的轮播图管理解决方案，包括以下功能：
- 轮播图的添加、编辑、删除
- 轮播图排序（权重管理）
- 轮播图状态管理（显示/隐藏）
- 移动端触摸滑动支持
- 响应式设计

## 安装步骤

### 1. 执行安装命令
```bash
php think install:banner
```
此命令将自动完成以下操作：
- 创建轮播图数据表
- 添加后台管理权限节点
- 导入示例数据

### 2. 配置文件上传路径
在 `application/extra/upload.php` 中确保已配置图片上传相关设置：
```php
return [
    'image' => [
        'mimes'    => 'jpg,png,gif,jpeg',
        'maxsize'  => '10mb',
    ],
];
```

### 3. 赋予权限
在后台管理系统中为需要管理轮播图的角色分配相应权限：
- 轮播图管理
  - 查看轮播图
  - 添加轮播图
  - 编辑轮播图
  - 删除轮播图
  - 批量更新

## 使用方法

### 后台管理
1. 登录后台管理系统
2. 在左侧菜单找到"轮播图管理"
3. 可以进行以下操作：
   - 添加新的轮播图
   - 编辑现有轮播图
   - 调整轮播图排序
   - 修改轮播图状态
   - 删除轮播图

### 前台调用
在需要显示轮播图的模板中添加以下代码：
```html
{include file="banner/index" /}
```

或者通过API获取轮播图数据：
```php
url: '/banner/getBanners'
method: 'GET'
response: {
    code: 1,
    msg: "success",
    data: [
        {
            "id": 1,
            "title": "轮播图标题",
            "image": "图片路径",
            "url": "链接地址",
            "weigh": 10,
            "status": "normal"
        }
        // ...
    ]
}
```

## 自定义配置

### 修改轮播图样式
可以通过修改以下文件自定义轮播图样式：
```css
/* 在你的CSS文件中添加 */
.banner-container {
    /* 自定义容器样式 */
}

.carousel-inner .item img {
    /* 自定义图片样式 */
}

.carousel-caption {
    /* 自定义标题样式 */
}
```

### 修改轮播效果
可以通过修改以下参数自定义轮播效果：
```javascript
$('#banner-carousel').carousel({
    interval: 5000, // 自动播放间隔，单位毫秒
    pause: 'hover', // 鼠标悬停时暂停
    wrap: true      // 循环播放
});
```

## 常见问题

### 1. 图片上传失败
- 检查上传目录权限是否正确
- 确认上传文件大小是否超过限制
- 验证文件类型是否在允许范围内

### 2. 轮播图不显示
- 确认数据库中是否有启用状态的轮播图
- 检查图片路径是否正确
- 验证是否正确引入了必要的CSS和JS文件

### 3. 触摸滑动无效
- 确认是否正确引入了bootstrap-touch-carousel插件
- 检查是否与其他JavaScript插件冲突

## 技术支持
如有问题，请提交Issue或联系技术支持。

## 更新日志
- 2023.06.01：初始版本发布
  - 基础轮播图功能
  - 移动端触摸支持
  - 响应式设计