<?php

namespace api\config;

/**
 * API配置类
 */
class ApiConfig
{
    /**
     * 精准查涉案API配置
     */
    const GOOD_CHECK_API = [
        'app_id' => '111', // 请替换为实际的appid
        'secret' => 'your_secret_key', // 请替换为实际的密钥
        'url' => 'https://api.k-wz.com/api/goodcheckapi',
    ];
    
    /**
     * 获取精准查涉案API配置
     * @return array
     */
    public static function getGoodCheckConfig()
    {
        return self::GOOD_CHECK_API;
    }
    
    /**
     * 从配置文件或环境变量获取配置
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        // 优先从环境变量获取
        $envValue = getenv($key);
        if ($envValue !== false) {
            return $envValue;
        }
        
        // 从配置数组获取
        $config = self::GOOD_CHECK_API;
        return isset($config[$key]) ? $config[$key] : $default;
    }
}
