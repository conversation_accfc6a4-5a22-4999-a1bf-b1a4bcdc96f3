<?php

namespace app\admin\validate;

use think\Validate;

class Banner extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'title' => 'require|max:100',
        'image' => 'require',
        'url'   => 'max:255',
        'weigh' => 'integer',
        'status'=> 'in:normal,hidden',
    ];
    
    /**
     * 提示消息
     */
    protected $message = [
        'title.require' => '标题不能为空',
        'title.max'     => '标题最多不能超过100个字符',
        'image.require' => '图片不能为空',
        'url.max'       => 'URL最多不能超过255个字符',
        'weigh.integer' => '权重必须是整数',
        'status.in'     => '状态必须是normal或hidden',
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['title', 'image', 'url', 'weigh', 'status'],
        'edit' => ['title', 'image', 'url', 'weigh', 'status'],
    ];
}