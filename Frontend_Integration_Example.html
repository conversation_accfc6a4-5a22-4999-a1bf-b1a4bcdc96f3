<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API查询系统 - 前端集成示例</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .checkbox-group { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 10px; }
        .checkbox-item { display: flex; align-items: center; padding: 10px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; transition: all 0.3s; }
        .checkbox-item:hover { background: #f0f8ff; border-color: #007bff; }
        .checkbox-item.selected { background: #e3f2fd; border-color: #2196f3; }
        .checkbox-item input { margin-right: 8px; }
        .price { color: #e91e63; font-weight: bold; margin-left: auto; }
        .package-section { background: #fff3e0; padding: 15px; border-radius: 8px; margin: 15px 0; border: 2px solid #ff9800; }
        .package-item { display: flex; align-items: center; justify-content: space-between; padding: 15px; background: white; border-radius: 8px; margin: 10px 0; border: 1px solid #ddd; cursor: pointer; }
        .package-item:hover { border-color: #ff9800; }
        .package-item.selected { border-color: #ff9800; background: #fff8e1; }
        .btn { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #545b62; }
        .result-section { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: #28a745; transition: width 0.3s; }
        .api-result { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: white; }
        .api-result.success { border-color: #28a745; }
        .api-result.error { border-color: #dc3545; }
        .total-price { font-size: 18px; font-weight: bold; color: #e91e63; text-align: right; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API查询系统</h1>
        
        <!-- 基本信息 -->
        <div class="form-group">
            <label for="name">姓名：</label>
            <input type="text" id="name" placeholder="请输入姓名" value="张三">
        </div>
        
        <div class="form-group">
            <label for="idCard">身份证号：</label>
            <input type="text" id="idCard" placeholder="请输入身份证号" value="******************">
        </div>
        
        <!-- 套餐选择 -->
        <div class="package-section">
            <h3>📦 套餐服务</h3>
            <div class="package-item" onclick="selectPackage('basic_report')">
                <div>
                    <input type="radio" name="package" value="basic_report" id="package_basic">
                    <label for="package_basic">基础查询报告服务</label>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        包含：民事案件、刑事案件、行政案件、非诚案件、执行案件、强制清算与破产条件、营销案件、验证案件
                    </div>
                </div>
                <div class="price">¥ 35.8</div>
            </div>
        </div>
        
        <!-- 单项选择 -->
        <div class="form-group">
            <h3>🎯 单项服务</h3>
            <div class="checkbox-group">
                <div class="checkbox-item" onclick="toggleItem('qiyu_prediction')">
                    <input type="checkbox" id="qiyu_prediction" value="qiyu_prediction">
                    <label for="qiyu_prediction">奇遇预测</label>
                    <span class="price">¥ 6.80</span>
                </div>
                
                <div class="checkbox-item" onclick="toggleItem('vehicle_info')">
                    <input type="checkbox" id="vehicle_info" value="vehicle_info">
                    <label for="vehicle_info">各下车辆</label>
                    <span class="price">¥ 15.80</span>
                </div>
                
                <div class="checkbox-item" onclick="toggleItem('credit_check')">
                    <input type="checkbox" id="credit_check" value="credit_check">
                    <label for="credit_check">信贷逾期</label>
                    <span class="price">¥ 4.80</span>
                </div>
                
                <div class="checkbox-item" onclick="toggleItem('employment_record')">
                    <input type="checkbox" id="employment_record" value="employment_record">
                    <label for="employment_record">人企任职记录</label>
                    <span class="price">¥ 12.80</span>
                </div>
                
                <div class="checkbox-item" onclick="toggleItem('education_check')">
                    <input type="checkbox" id="education_check" value="education_check">
                    <label for="education_check">学历查询</label>
                    <span class="price">¥ 5.80</span>
                </div>
                
                <div class="checkbox-item" onclick="toggleItem('marriage_status')">
                    <input type="checkbox" id="marriage_status" value="marriage_status">
                    <label for="marriage_status">婚姻状况</label>
                    <span class="price">¥ 15.80</span>
                </div>
                
                <div class="checkbox-item" onclick="toggleItem('risk_list')">
                    <input type="checkbox" id="risk_list" value="risk_list">
                    <label for="risk_list">风险清单</label>
                    <span class="price">¥ 15.80</span>
                </div>
            </div>
        </div>
        
        <!-- 查询策略 -->
        <div class="form-group">
            <label for="strategy">查询策略：</label>
            <select id="strategy">
                <option value="auto">自动选择（推荐）</option>
                <option value="sync">同步查询（1-3个API）</option>
                <option value="parallel">并行查询（4+个API）</option>
                <option value="queue">队列查询（批量处理）</option>
            </select>
        </div>
        
        <!-- 总价显示 -->
        <div class="total-price">
            总价：¥ <span id="totalPrice">0.00</span>
        </div>
        
        <!-- 操作按钮 -->
        <div style="text-align: center;">
            <button class="btn" onclick="submitQuery()">🚀 开始查询</button>
            <button class="btn btn-secondary" onclick="resetForm()">🔄 重置</button>
        </div>
        
        <!-- 结果显示 -->
        <div id="resultSection" class="result-section" style="display: none;">
            <h3>📊 查询结果</h3>
            <div id="progressContainer" style="display: none;">
                <div>查询进度：<span id="progressText">0%</span></div>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
            </div>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        let selectedItems = [];
        let selectedPackage = '';
        let currentTaskId = '';
        
        // 切换单项选择
        function toggleItem(itemId) {
            const checkbox = document.getElementById(itemId);
            const item = checkbox.closest('.checkbox-item');
            
            if (checkbox.checked) {
                checkbox.checked = false;
                item.classList.remove('selected');
                selectedItems = selectedItems.filter(id => id !== itemId);
            } else {
                checkbox.checked = true;
                item.classList.add('selected');
                selectedItems.push(itemId);
                
                // 取消套餐选择
                if (selectedPackage) {
                    document.querySelector('input[name="package"]:checked').checked = false;
                    document.querySelector('.package-item.selected').classList.remove('selected');
                    selectedPackage = '';
                }
            }
            
            updateTotalPrice();
        }
        
        // 选择套餐
        function selectPackage(packageId) {
            const radio = document.getElementById('package_' + packageId);
            const item = radio.closest('.package-item');
            
            if (selectedPackage === packageId) {
                // 取消选择
                radio.checked = false;
                item.classList.remove('selected');
                selectedPackage = '';
            } else {
                // 选择套餐
                document.querySelectorAll('.package-item').forEach(el => el.classList.remove('selected'));
                radio.checked = true;
                item.classList.add('selected');
                selectedPackage = packageId;
                
                // 取消单项选择
                selectedItems = [];
                document.querySelectorAll('.checkbox-item input').forEach(cb => {
                    cb.checked = false;
                    cb.closest('.checkbox-item').classList.remove('selected');
                });
            }
            
            updateTotalPrice();
        }
        
        // 更新总价
        function updateTotalPrice() {
            let total = 0;
            
            if (selectedPackage === 'basic_report') {
                total = 35.8;
            } else {
                const prices = {
                    'qiyu_prediction': 6.80,
                    'vehicle_info': 15.80,
                    'credit_check': 4.80,
                    'employment_record': 12.80,
                    'education_check': 5.80,
                    'marriage_status': 15.80,
                    'risk_list': 15.80
                };
                
                selectedItems.forEach(item => {
                    total += prices[item] || 0;
                });
            }
            
            document.getElementById('totalPrice').textContent = total.toFixed(2);
        }
        
        // 提交查询
        function submitQuery() {
            const name = document.getElementById('name').value.trim();
            const idCard = document.getElementById('idCard').value.trim();
            const strategy = document.getElementById('strategy').value;
            
            if (!name || !idCard) {
                alert('请填写姓名和身份证号');
                return;
            }
            
            if (!selectedPackage && selectedItems.length === 0) {
                alert('请选择查询项目或套餐');
                return;
            }
            
            // 构建查询参数
            const queryData = {
                name: name,
                id_card: idCard,
                strategy: strategy,
                selected_items: selectedItems,
                selected_package: selectedPackage
            };
            
            // 显示结果区域
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('resultContent').innerHTML = '<div>正在提交查询...</div>';
            
            // 发送查询请求
            fetch('/index/query', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams(queryData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (strategy === 'queue') {
                        // 队列查询，开始轮询状态
                        currentTaskId = data.task_id;
                        showQueueProgress(data);
                        pollQueueStatus();
                    } else {
                        // 实时查询，直接显示结果
                        showQueryResults(data);
                    }
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                showError('查询请求失败：' + error.message);
            });
        }
        
        // 显示队列进度
        function showQueueProgress(data) {
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('resultContent').innerHTML = `
                <div>
                    <h4>📋 任务已提交到队列</h4>
                    <p>任务ID：${data.task_id}</p>
                    <p>总任务数：${data.total_tasks}</p>
                    <p>预计完成时间：${data.estimated_time}</p>
                </div>
            `;
        }
        
        // 轮询队列状态
        function pollQueueStatus() {
            if (!currentTaskId) return;
            
            fetch(`/index/checkQueueStatus?task_id=${currentTaskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateProgress(data.progress);
                        
                        if (data.status === 'processing') {
                            // 继续轮询
                            setTimeout(pollQueueStatus, 3000);
                        } else {
                            // 完成，显示结果
                            document.getElementById('progressContainer').style.display = 'none';
                            showQueueResults(data);
                        }
                    }
                })
                .catch(error => {
                    console.error('轮询状态失败：', error);
                    setTimeout(pollQueueStatus, 5000); // 5秒后重试
                });
        }
        
        // 更新进度
        function updateProgress(progress) {
            const percentage = progress.percentage;
            document.getElementById('progressText').textContent = `${percentage}% (${progress.completed}/${progress.total})`;
            document.getElementById('progressFill').style.width = `${percentage}%`;
        }
        
        // 显示查询结果
        function showQueryResults(data) {
            let html = `
                <div>
                    <h4>✅ 查询完成</h4>
                    <p>查询时间：${data.query_info.total_time}</p>
                    <p>总价：¥${data.query_info.query_config.total_price}</p>
                </div>
            `;
            
            if (data.results && data.results.results) {
                Object.entries(data.results.results).forEach(([key, result]) => {
                    const statusClass = result.success ? 'success' : 'error';
                    html += `
                        <div class="api-result ${statusClass}">
                            <h5>${result.api_name}</h5>
                            <p>状态：${result.success ? '✅ 成功' : '❌ 失败'}</p>
                            <p>耗时：${result.query_time}</p>
                            <p>价格：¥${result.price}</p>
                            <p>消息：${result.message}</p>
                        </div>
                    `;
                });
            }
            
            if (data.summary) {
                html += `
                    <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4>📈 风险摘要</h4>
                        <p>风险等级：${data.summary.risk_level}</p>
                        <p>风险因素：${data.summary.risk_factors.join(', ')}</p>
                        <p>建议：${data.summary.recommendations.join(', ')}</p>
                    </div>
                `;
            }
            
            document.getElementById('resultContent').innerHTML = html;
        }
        
        // 显示队列结果
        function showQueueResults(data) {
            let html = `
                <div>
                    <h4>✅ 队列任务完成</h4>
                    <p>任务ID：${data.task_id}</p>
                    <p>完成状态：${data.status}</p>
                </div>
            `;
            
            if (data.results && data.results.completed) {
                data.results.completed.forEach(result => {
                    html += `
                        <div class="api-result success">
                            <h5>${result.api_name}</h5>
                            <p>状态：✅ 成功</p>
                            <p>耗时：${result.query_time}</p>
                            <p>完成时间：${result.completed_at}</p>
                        </div>
                    `;
                });
            }
            
            if (data.results && data.results.failed) {
                data.results.failed.forEach(result => {
                    html += `
                        <div class="api-result error">
                            <h5>${result.api_name}</h5>
                            <p>状态：❌ 失败</p>
                            <p>错误：${result.message}</p>
                        </div>
                    `;
                });
            }
            
            document.getElementById('resultContent').innerHTML = html;
        }
        
        // 显示错误
        function showError(message) {
            document.getElementById('resultContent').innerHTML = `
                <div class="api-result error">
                    <h5>❌ 查询失败</h5>
                    <p>${message}</p>
                </div>
            `;
        }
        
        // 重置表单
        function resetForm() {
            selectedItems = [];
            selectedPackage = '';
            currentTaskId = '';
            
            document.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                cb.checked = false;
                cb.closest('.checkbox-item').classList.remove('selected');
            });
            
            document.querySelectorAll('input[type="radio"]').forEach(radio => {
                radio.checked = false;
            });
            
            document.querySelectorAll('.package-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            document.getElementById('resultSection').style.display = 'none';
            updateTotalPrice();
        }
        
        // 初始化
        updateTotalPrice();
    </script>
</body>
</html>
