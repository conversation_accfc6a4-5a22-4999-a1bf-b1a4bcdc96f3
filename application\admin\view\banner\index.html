<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add,edit,del')}
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('banner/edit')}" 
                           data-operate-del="{:$auth->check('banner/del')}" 
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'banner/index' + location.search,
                    add_url: 'banner/add',
                    edit_url: 'banner/edit',
                    del_url: 'banner/del',
                    multi_url: 'banner/multi',
                    table: 'banner',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'title', title: __('Title')},
                        {
                            field: 'image', 
                            title: __('Image'), 
                            events: Table.api.events.image, 
                            formatter: Table.api.formatter.image, 
                            operate: false
                        },
                        {field: 'url', title: __('Url'), formatter: Table.api.formatter.url},
                        {field: 'weigh', title: __('Weigh'), sortable: true},
                        {
                            field: 'status', 
                            title: __('Status'), 
                            searchList: {"normal":__('Normal'),"hidden":__('Hidden')},
                            formatter: Table.api.formatter.status
                        },
                        {
                            field: 'createtime', 
                            title: __('Createtime'), 
                            sortable: true, 
                            operate: 'RANGE', 
                            addclass: 'datetimerange', 
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'updatetime', 
                            title: __('Updatetime'), 
                            sortable: true, 
                            operate: 'RANGE', 
                            addclass: 'datetimerange', 
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate', 
                            title: __('Operate'), 
                            table: table, 
                            events: Table.api.events.operate, 
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
</script>