<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\ServiceItem as ServiceItemModel;

/**
 * 服务项目管理（固定项目，只能编辑价格）
 */
class Serviceitem extends Backend
{
    /**
     * ServiceItem模型对象
     * @var \app\common\model\ServiceItem
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new ServiceItemModel;

        $this->view->assign("statusList", $this->model->getStatusList());
    }


    /**
     * 编辑（只允许编辑价格和状态）
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 只允许编辑特定字段
                $allowFields = ['current_price', 'original_price', 'status', 'weigh'];
                $updateData = [];
                foreach ($allowFields as $field) {
                    if (isset($params[$field])) {
                        $updateData[$field] = $params[$field];
                    }
                }
                
                $result = false;
                \think\Db::startTrans();
                try {
                    $result = $row->allowField($allowFields)->save($updateData);
                    \think\Db::commit();
                } catch (\think\exception\PDOException $e) {
                    \think\Db::rollback();
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    \think\Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 禁用添加功能
     */
    public function add()
    {
        $this->error('服务项目为固定项目，不允许添加');
    }

    /**
     * 禁用删除功能
     */
    public function del($ids = null)
    {
        $this->error('服务项目为固定项目，不允许删除');
    }

    /**
     * 批量更新（只允许更新状态）
     */
    public function multi($ids = null)
    {
        // 管理员检测
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isPost()) {
            $ids = $this->request->post("ids");
            $row = $this->request->post("row/a");
            if ($ids) {
                $pk = $this->model->getPk();
                $adminIds = $this->getDataLimitAdminIds();
                if (is_array($adminIds)) {
                    $this->model->where($this->dataLimitField, 'in', $adminIds);
                }
                $list = $this->model->where($pk, 'in', $ids)->select();

                $count = 0;
                \think\Db::startTrans();
                try {
                    foreach ($list as $index => $item) {
                        // 只允许批量更新状态和权重
                        $allowFields = ['status', 'weigh'];
                        $updateData = [];
                        foreach ($allowFields as $field) {
                            if (isset($row[$field])) {
                                $updateData[$field] = $row[$field];
                            }
                        }

                        if (!empty($updateData)) {
                            $count += $item->allowField($allowFields)->save($updateData);
                        }
                    }
                    \think\Db::commit();
                } catch (\think\Exception $e) {
                    \think\Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($count) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        return $this->view->fetch();
    }
}
