<div class="banner-component">
    <div id="carousel-banner-component" class="carousel slide" data-ride="carousel">
        <!-- 指示器 -->
        <ol class="carousel-indicators">
            {foreach name="bannerList" item="item" key="key"}
            <li data-target="#carousel-banner-component" data-slide-to="{$key}" {if $key==0}class="active"{/if}></li>
            {/foreach}
        </ol>

        <!-- 轮播项目 -->
        <div class="carousel-inner" role="listbox">
            {foreach name="bannerList" item="item" key="key"}
            <div class="item {if $key==0}active{/if}">
                {if $item.url}
                <a href="{$item.url}" target="_blank">
                    <img src="{$item.image}" alt="{$item.title}">
                </a>
                {else}
                <img src="{$item.image}" alt="{$item.title}">
                {/if}
                <div class="carousel-caption">
                    {$item.title}
                </div>
            </div>
            {/foreach}
        </ol>

        <!-- 控制按钮 -->
        <a class="left carousel-control" href="#carousel-banner-component" role="button" data-slide="prev">
            <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
            <span class="sr-only">上一个</span>
        </a>
        <a class="right carousel-control" href="#carousel-banner-component" role="button" data-slide="next">
            <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
            <span class="sr-only">下一个</span>
        </a>
    </div>
</div>

<style>
.banner-component {
    margin-bottom: 20px;
}
.carousel-inner .item img {
    width: 100%;
    height: auto;
}
</style>

<script>
$(function() {
    // 自动轮播
    $('#carousel-banner-component').carousel({
        interval: 5000
    });
});
</script>