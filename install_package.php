<?php
/**
 * 套餐管理安装脚本
 * 运行此脚本来创建套餐表和初始化数据
 */

// 引入ThinkPHP框架
require_once 'thinkphp/start.php';

use think\Db;
use think\Exception;

try {
    echo "开始安装套餐管理模块...\n";
    
    // 1. 创建套餐表
    echo "1. 创建套餐表...\n";
    $packageSql = file_get_contents('application/admin/command/Install/package.sql');
    
    // 分割SQL语句
    $sqlStatements = explode(';', $packageSql);
    
    foreach ($sqlStatements as $sql) {
        $sql = trim($sql);
        if (!empty($sql)) {
            Db::execute($sql);
        }
    }
    
    echo "   套餐表创建成功！\n";
    
    // 2. 添加后台菜单
    echo "2. 添加后台管理菜单...\n";
    
    // 检查是否已存在套餐管理菜单
    $existMenu = Db::name('auth_rule')->where('name', 'package')->find();
    
    if (!$existMenu) {
        // 添加主菜单
        $packageMenuId = Db::name('auth_rule')->insertGetId([
            'type' => 'file',
            'pid' => 0,
            'name' => 'package',
            'title' => '套餐管理',
            'icon' => 'fa fa-cube',
            'condition' => '',
            'remark' => '套餐管理',
            'ismenu' => 1,
            'createtime' => time(),
            'updatetime' => time(),
            'weigh' => 0,
            'status' => 'normal'
        ]);
        
        // 添加子菜单
        $subMenus = [
            ['name' => 'package/index', 'title' => '查看'],
            ['name' => 'package/add', 'title' => '添加'],
            ['name' => 'package/edit', 'title' => '编辑'],
            ['name' => 'package/del', 'title' => '删除'],
            ['name' => 'package/multi', 'title' => '批量更新']
        ];
        
        foreach ($subMenus as $menu) {
            Db::name('auth_rule')->insert([
                'type' => 'file',
                'pid' => $packageMenuId,
                'name' => $menu['name'],
                'title' => $menu['title'],
                'icon' => 'fa fa-circle-o',
                'condition' => '',
                'remark' => '',
                'ismenu' => 1,
                'createtime' => time(),
                'updatetime' => time(),
                'weigh' => 0,
                'status' => 'normal'
            ]);
        }
        
        echo "   后台管理菜单添加成功！\n";
    } else {
        echo "   后台管理菜单已存在，跳过创建。\n";
    }
    
    // 3. 检查数据是否已存在
    echo "3. 检查初始数据...\n";
    $existData = Db::name('package')->count();
    
    if ($existData == 0) {
        echo "   初始数据已在创建表时插入。\n";
    } else {
        echo "   数据已存在，共 {$existData} 条记录。\n";
    }
    
    echo "\n安装完成！\n";
    echo "现在你可以：\n";
    echo "1. 登录后台管理系统，在菜单中找到'套餐管理'进行管理\n";
    echo "2. 前端页面的套餐选项将自动从数据库加载\n";
    echo "3. 可以通过API接口 /api/package 获取套餐数据\n";
    
} catch (Exception $e) {
    echo "安装失败：" . $e->getMessage() . "\n";
    echo "请检查数据库连接和权限设置。\n";
}
