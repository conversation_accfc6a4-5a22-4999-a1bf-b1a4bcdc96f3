<?php
/**
 * 套餐管理安装脚本
 * 运行此脚本来创建套餐表和初始化数据
 */

// 引入ThinkPHP框架
require_once 'thinkphp/start.php';

use think\Db;
use think\Exception;

try {
    echo "开始安装套餐管理模块...\n";
    
    // 1. 创建数据表
    echo "1. 创建数据表...\n";
    $packageSql = file_get_contents('application/admin/command/Install/package_new.sql');
    
    // 分割SQL语句
    $sqlStatements = explode(';', $packageSql);
    
    foreach ($sqlStatements as $sql) {
        $sql = trim($sql);
        if (!empty($sql)) {
            Db::execute($sql);
        }
    }
    
    echo "   数据表创建成功！\n";
    
    // 2. 添加后台菜单
    echo "2. 添加后台管理菜单...\n";
    
    // 检查是否已存在套餐管理菜单
    $existMenu = Db::name('auth_rule')->where('name', 'package')->find();
    
    if (!$existMenu) {
        // 添加主菜单
        $packageMenuId = Db::name('auth_rule')->insertGetId([
            'type' => 'file',
            'pid' => 0,
            'name' => 'package',
            'title' => '套餐管理',
            'icon' => 'fa fa-cube',
            'condition' => '',
            'remark' => '套餐管理',
            'ismenu' => 1,
            'createtime' => time(),
            'updatetime' => time(),
            'weigh' => 0,
            'status' => 'normal'
        ]);
        
        // 添加套餐管理子菜单
        $packageSubMenus = [
            ['name' => 'package/index', 'title' => '套餐列表'],
            ['name' => 'package/add', 'title' => '添加套餐'],
            ['name' => 'package/edit', 'title' => '编辑套餐'],
            ['name' => 'package/del', 'title' => '删除套餐'],
            ['name' => 'package/multi', 'title' => '批量更新']
        ];

        foreach ($packageSubMenus as $menu) {
            Db::name('auth_rule')->insert([
                'type' => 'file',
                'pid' => $packageMenuId,
                'name' => $menu['name'],
                'title' => $menu['title'],
                'icon' => 'fa fa-circle-o',
                'condition' => '',
                'remark' => '',
                'ismenu' => 1,
                'createtime' => time(),
                'updatetime' => time(),
                'weigh' => 0,
                'status' => 'normal'
            ]);
        }

        // 添加服务项目管理菜单
        $serviceItemMenuId = Db::name('auth_rule')->insertGetId([
            'type' => 'file',
            'pid' => 0,
            'name' => 'serviceitem',
            'title' => '服务项目管理',
            'icon' => 'fa fa-list',
            'condition' => '',
            'remark' => '服务项目管理',
            'ismenu' => 1,
            'createtime' => time(),
            'updatetime' => time(),
            'weigh' => 0,
            'status' => 'normal'
        ]);

        // 添加服务项目子菜单
        $serviceSubMenus = [
            ['name' => 'serviceitem/index', 'title' => '服务项目列表'],
            ['name' => 'serviceitem/edit', 'title' => '编辑服务项目']
        ];

        foreach ($serviceSubMenus as $menu) {
            Db::name('auth_rule')->insert([
                'type' => 'file',
                'pid' => $serviceItemMenuId,
                'name' => $menu['name'],
                'title' => $menu['title'],
                'icon' => 'fa fa-circle-o',
                'condition' => '',
                'remark' => '',
                'ismenu' => 1,
                'createtime' => time(),
                'updatetime' => time(),
                'weigh' => 0,
                'status' => 'normal'
            ]);
        }
        
        echo "   后台管理菜单添加成功！\n";
    } else {
        echo "   后台管理菜单已存在，跳过创建。\n";
    }
    
    // 3. 检查数据是否已存在
    echo "3. 检查初始数据...\n";
    $existServiceData = Db::name('service_item')->count();
    $existPackageData = Db::name('package')->count();

    echo "   服务项目数据：{$existServiceData} 条\n";
    echo "   套餐数据：{$existPackageData} 条\n";
    
    echo "\n安装完成！\n";
    echo "现在你可以：\n";
    echo "1. 登录后台管理系统，管理套餐和服务项目\n";
    echo "   - '套餐管理'：创建和编辑套餐组合\n";
    echo "   - '服务项目管理'：编辑固定服务项目的价格\n";
    echo "2. 前端页面将显示：\n";
    echo "   - 单项：固定的6个服务项目\n";
    echo "   - 套餐：后台配置的服务项目组合\n";
    echo "3. 可以通过API接口获取数据\n";
    
} catch (Exception $e) {
    echo "安装失败：" . $e->getMessage() . "\n";
    echo "请检查数据库连接和权限设置。\n";
}
