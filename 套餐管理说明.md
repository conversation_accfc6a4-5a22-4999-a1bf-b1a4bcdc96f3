# 套餐管理功能说明

## 📋 功能概述

已成功将原本硬编码在前端的套餐选项改为后台动态管理，现在管理员可以通过后台界面灵活地添加、编辑、删除套餐选项。

## 🗂️ 文件结构

### 📁 模型文件
- `application/common/model/Package.php` - 套餐数据模型

### 📁 控制器文件
- `application/admin/controller/Package.php` - 后台管理控制器
- `application/api/controller/Package.php` - API接口控制器
- `application/index/controller/Index.php` - 前端首页控制器（已修改）

### 📁 视图文件
- `application/admin/view/package/index.html` - 后台套餐列表页
- `application/admin/view/package/add.html` - 后台添加套餐页
- `application/admin/view/package/edit.html` - 后台编辑套餐页
- `application/index/view/index/index.html` - 前端首页（已修改）

### 📁 数据库文件
- `application/admin/command/Install/package.sql` - 套餐表结构和初始数据
- `application/admin/command/Install/package_menu.sql` - 后台菜单SQL
- `install_package.php` - 安装脚本

## 🚀 安装步骤

### 1. 执行安装脚本
```bash
php install_package.php
```

### 2. 手动执行SQL（如果安装脚本失败）
```sql
-- 执行套餐表创建
source application/admin/command/Install/package.sql

-- 执行菜单添加（可选，安装脚本会自动处理）
source application/admin/command/Install/package_menu.sql
```

## 📊 数据库表结构

### fa_package 表字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int(10) | 主键ID |
| name | varchar(100) | 套餐名称 |
| description | varchar(255) | 套餐描述 |
| type | enum | 类型：single=单项，package=套餐 |
| current_price | decimal(10,2) | 当前价格 |
| original_price | decimal(10,2) | 原价 |
| tags | text | 标签（逗号分隔） |
| weigh | int(10) | 权重（排序用） |
| status | enum | 状态：normal=正常，hidden=隐藏 |
| createtime | bigint(16) | 创建时间 |
| updatetime | bigint(16) | 更新时间 |

## 🎛️ 后台管理功能

### 访问路径
- 后台管理：`/admin/package`
- 套餐列表：`/admin/package/index`
- 添加套餐：`/admin/package/add`
- 编辑套餐：`/admin/package/edit`

### 管理功能
- ✅ 查看套餐列表
- ✅ 添加新套餐
- ✅ 编辑套餐信息
- ✅ 删除套餐
- ✅ 批量操作（启用/禁用）
- ✅ 按状态筛选
- ✅ 搜索功能

## 🔌 API接口

### 获取所有套餐
```
GET /api/package
返回：{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "single": [...],
    "package": [...]
  }
}
```

### 获取单项列表
```
GET /api/package/single
```

### 获取套餐列表
```
GET /api/package/package
```

### 获取套餐详情
```
GET /api/package/detail?id=1
```

## 🎨 前端显示

### 动态加载
前端页面现在会自动从数据库加载套餐数据：

- **单项选择**：显示 `type='single'` 的套餐
- **套餐选择**：显示 `type='package'` 的套餐
- **标签显示**：自动解析 `tags` 字段并显示

### 模板语法
```html
<!-- 单项列表 -->
{foreach name="singleList" item="item"}
<div class="single-item" data-id="{$item.id}">
    <h4>{$item.name}</h4>
    <p>{$item.description}</p>
    <div class="price">¥ {$item.current_price}</div>
</div>
{/foreach}

<!-- 套餐列表 -->
{foreach name="packageList" item="item"}
<div class="module-item" data-id="{$item.id}">
    <span class="module-name">{$item.name}</span>
    <div class="module-tags">
        {foreach name="item.tags" item="tag"}
        <span class="module-tag">{$tag}</span>
        {/foreach}
    </div>
</div>
{/foreach}
```

## 📝 使用说明

### 1. 添加新套餐
1. 登录后台管理系统
2. 找到"套餐管理"菜单
3. 点击"添加"按钮
4. 填写套餐信息：
   - 套餐名称
   - 套餐描述
   - 类型（单项/套餐）
   - 当前价格和原价
   - 标签（多个标签用逗号分隔）
   - 权重（用于排序）
   - 状态（正常/隐藏）

### 2. 编辑套餐
1. 在套餐列表中找到要编辑的套餐
2. 点击"编辑"按钮
3. 修改相关信息
4. 保存更改

### 3. 管理套餐状态
- **正常**：在前端显示
- **隐藏**：在前端不显示

### 4. 排序管理
通过"权重"字段控制套餐在前端的显示顺序，权重越大越靠前。

## 🔧 技术特点

### 1. 数据分离
- 套餐数据完全从数据库加载
- 前端模板使用动态数据
- 支持实时更新

### 2. 灵活管理
- 支持两种套餐类型
- 标签系统支持
- 权重排序
- 状态控制

### 3. API支持
- RESTful API设计
- 支持多种查询方式
- 标准JSON返回格式

### 4. 后台集成
- 完整的CRUD操作
- 批量管理功能
- 搜索和筛选
- 权限控制

## 🎯 优势

1. **灵活性**：管理员可随时调整套餐内容
2. **可维护性**：代码结构清晰，易于维护
3. **扩展性**：支持添加更多字段和功能
4. **用户体验**：前端显示实时反映后台设置

现在你的套餐选项已经完全由后台管理，可以灵活地添加、修改和删除套餐选项了！
