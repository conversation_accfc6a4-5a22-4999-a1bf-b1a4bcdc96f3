define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数
            Table.api.init({
                extend: {
                    index_url: 'package/index' + location.search,
                    add_url: 'package/add',
                    edit_url: 'package/edit',
                    del_url: 'package/del',
                    multi_url: 'package/multi',
                    import_url: 'package/import',
                    table: 'package',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'description', title: __('Description'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'service_items', title: __('Service items'), operate: false, formatter: function(value, row, index) {
                            if (!value) return '';
                            // 这里需要根据service_items_list来显示
                            if (row.service_items_list && row.service_items_list.length > 0) {
                                var html = '';
                                $.each(row.service_items_list, function(i, item) {
                                    html += '<span class="label label-success" style="margin-right:3px;">' + item.name + '</span>';
                                });
                                return html;
                            }
                            return '';
                        }},
                        {field: 'current_price', title: __('Current price'), operate:'BETWEEN', sortable: true},
                        {field: 'original_price', title: __('Original price'), operate:'BETWEEN', sortable: true},

                        {field: 'weigh', title: __('Weigh'), operate: false, sortable: true},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
