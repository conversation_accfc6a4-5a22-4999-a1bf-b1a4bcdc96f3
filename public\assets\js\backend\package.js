define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数
            Table.api.init({
                extend: {
                    index_url: 'package/index' + location.search,
                    add_url: 'package/add',
                    edit_url: 'package/edit',
                    del_url: 'package/del',
                    multi_url: 'package/multi',
                    import_url: 'package/import',
                    table: 'package',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'description', title: __('Description'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'type', title: __('Type'), searchList: {"single":__('Single'),"package":__('Package')}, formatter: Table.api.formatter.normal},
                        {field: 'current_price', title: __('Current price'), operate:'BETWEEN', sortable: true},
                        {field: 'original_price', title: __('Original price'), operate:'BETWEEN', sortable: true},
                        {field: 'tags', title: __('Tags'), operate: 'LIKE', formatter: function(value, row, index) {
                            if (!value) return '';
                            var tags = value.split(',');
                            var html = '';
                            $.each(tags, function(i, tag) {
                                if (tag.trim()) {
                                    html += '<span class="label label-info" style="margin-right:3px;">' + tag.trim() + '</span>';
                                }
                            });
                            return html;
                        }},
                        {field: 'weigh', title: __('Weigh'), operate: false, sortable: true},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
