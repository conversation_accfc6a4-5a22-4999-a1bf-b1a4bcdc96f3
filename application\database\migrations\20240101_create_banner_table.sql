--
-- 轮播图表结构
--

CREATE TABLE IF NOT EXISTS `fa_banner` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '图片',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '链接',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';

--
-- 插入示例数据
--

INSERT INTO `fa_banner` (`title`, `image`, `url`, `weigh`, `status`, `createtime`, `updatetime`) VALUES
('欢迎使用FastAdmin', '/assets/img/banner/welcome.jpg', 'https://www.fastadmin.net', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('轮播图示例', '/assets/img/banner/example.jpg', 'https://www.fastadmin.net/store.html', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

--
-- 更新菜单权重
--

UPDATE `fa_auth_rule` SET `weigh` = 100 WHERE `name` = 'banner';