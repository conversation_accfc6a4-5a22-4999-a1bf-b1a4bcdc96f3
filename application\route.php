<?php

use think\Route;

// 注册路由到index模块的Banner控制器的index操作
Route::get('banner', 'index/banner/index');
Route::get('banner/index', 'index/banner/index');
Route::get('banner/getBanners', 'index/banner/getBanners');

// 其他路由保持不变
return [
    //别名配置,别名只能是映射到控制器且访问时必须加上请求的方法
    '__alias__'   => [
    ],
    //变量规则
    '__pattern__' => [
    ],
    //路由规则
    '__rest__'    => [
    ],
    //全局变量规则
    '__rules__'   => [
    ],
];