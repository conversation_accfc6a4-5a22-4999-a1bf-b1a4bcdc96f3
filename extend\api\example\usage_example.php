<?php

/**
 * 精准查涉案API使用示例
 */

// 引入自动加载（如果使用Composer）或手动引入文件
require_once '../GoodCheckApi.php';
require_once '../config/ApiConfig.php';
require_once '../ApiFactory.php';

use api\GoodCheckApi;
use api\ApiFactory;

// 示例1：直接使用GoodCheckApi类
echo "=== 示例1：直接使用GoodCheckApi类 ===\n";

$appId = '111'; // 替换为实际的appid
$secret = 'your_secret_key'; // 替换为实际的密钥

$api = new GoodCheckApi($appId, $secret);

// 个人涉案查询
$result = $api->checkPerson('张三', '420101199001010001');
echo "个人查询结果：\n";
print_r($result);

// 企业涉案查询
$result = $api->checkCompany('某某科技有限公司', '91110000123456789X');
echo "企业查询结果：\n";
print_r($result);

echo "\n";

// 示例2：使用ApiFactory工厂类
echo "=== 示例2：使用ApiFactory工厂类 ===\n";

// 快速个人查询
$result = ApiFactory::quickCheckPerson('李四', '420101199002020002', [
    'orderno' => 'ORDER_' . time(),
    'client_ip' => '***********',
    'interfacecoding' => 'sf003'
]);
echo "快速个人查询结果：\n";
print_r($result);

// 快速企业查询
$result = ApiFactory::quickCheckCompany('另一家科技有限公司', '91110000987654321Y', [
    'orderno' => 'ORDER_' . time(),
    'client_ip' => '***********'
]);
echo "快速企业查询结果：\n";
print_r($result);

echo "\n";

// 示例3：自定义参数查询
echo "=== 示例3：自定义参数查询 ===\n";

$api = ApiFactory::createGoodCheckApi();

$customParams = [
    'orderno' => 'CUSTOM_ORDER_' . time(),
    'name' => ['王五', '赵六'], // 支持多个姓名查询
    'code' => '420101199003030003',
    'detail' => '1', // 详细版
    'client_ip' => '**************',
    'interfacecoding' => 'custom_001'
];

$result = $api->goodCheck($customParams);
echo "自定义参数查询结果：\n";
print_r($result);

echo "\n";

// 示例4：错误处理
echo "=== 示例4：错误处理示例 ===\n";

$result = ApiFactory::quickCheckPerson('', ''); // 传入空参数测试错误处理

if ($result['success']) {
    echo "查询成功：\n";
    print_r($result['data']);
} else {
    echo "查询失败：" . $result['message'] . "\n";
    if ($result['data']) {
        echo "原始响应：" . $result['data'] . "\n";
    }
}

/**
 * 在ThinkPHP控制器中的使用示例：
 * 
 * <?php
 * namespace app\index\controller;
 * 
 * use think\Controller;
 * use api\ApiFactory;
 * 
 * class CaseCheck extends Controller
 * {
 *     public function checkPerson()
 *     {
 *         $name = $this->request->param('name');
 *         $idCard = $this->request->param('id_card');
 *         
 *         if (empty($name) || empty($idCard)) {
 *             return json(['code' => 400, 'msg' => '参数不能为空']);
 *         }
 *         
 *         $options = [
 *             'orderno' => 'ORDER_' . time(),
 *             'client_ip' => $this->request->ip(),
 *             'interfacecoding' => 'web_query_001'
 *         ];
 *         
 *         $result = ApiFactory::quickCheckPerson($name, $idCard, $options);
 *         
 *         if ($result['success']) {
 *             return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
 *         } else {
 *             return json(['code' => 500, 'msg' => $result['message']]);
 *         }
 *     }
 *     
 *     public function checkCompany()
 *     {
 *         $companyName = $this->request->param('company_name');
 *         $taxNumber = $this->request->param('tax_number');
 *         
 *         if (empty($companyName) || empty($taxNumber)) {
 *             return json(['code' => 400, 'msg' => '参数不能为空']);
 *         }
 *         
 *         $options = [
 *             'orderno' => 'COMPANY_ORDER_' . time(),
 *             'client_ip' => $this->request->ip()
 *         ];
 *         
 *         $result = ApiFactory::quickCheckCompany($companyName, $taxNumber, $options);
 *         
 *         if ($result['success']) {
 *             return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
 *         } else {
 *             return json(['code' => 500, 'msg' => $result['message']]);
 *         }
 *     }
 * }
 */
