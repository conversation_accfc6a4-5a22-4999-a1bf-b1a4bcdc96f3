<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>征信报告示例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 480px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
        }

        /* 头部区域 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            position: relative;
        }

        .header-time {
            font-size: 12px;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .header-text {
            font-size: 13px;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .get-report-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
        }

        .service-notice {
            background: rgba(255, 255, 255, 0.15);
            padding: 8px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin-bottom: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .service-desc {
            font-size: 12px;
            opacity: 0.9;
        }

        /* 身份验证区域 */
        .identity-section {
            background: white;
            margin: 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .identity-header {
            background: #f0f8ff;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .identity-title {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
        }

        .identity-icon {
            width: 20px;
            height: 20px;
            background: #4285f4;
            border-radius: 50%;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .verify-status {
            background: #4285f4;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
        }

        .identity-info {
            padding: 16px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #666;
            font-size: 14px;
        }

        .info-value {
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }

        /* 信息综述区域 */
        .summary-section {
            margin: 20px;
        }

        .summary-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .summary-title {
            color: #4285f4;
            font-size: 16px;
            font-weight: 600;
            margin-right: 10px;
        }

        .summary-subtitle {
            color: #999;
            font-size: 12px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            margin-bottom: 20px;
        }

        .summary-item {
            background: #f8f9ff;
            padding: 12px 8px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e8eaff;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .summary-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        /* 民事案件 - 粉红色 */
        .summary-item.civil {
            background: linear-gradient(135deg, #FFE5E5 0%, #FFF0F0 100%);
            border: 1px solid #FFD1D1;
        }

        .summary-item.civil .summary-icon {
            color: #FF6B6B;
        }

        /* 刑事案件 - 紫色 */
        .summary-item.criminal {
            background: linear-gradient(135deg, #E8D5FF 0%, #F0E6FF 100%);
            border: 1px solid #D1B3FF;
        }

        .summary-item.criminal .summary-icon {
            color: #9C27B0;
        }

        /* 行政案件 - 蓝色 */
        .summary-item.administrative {
            background: linear-gradient(135deg, #E3F2FD 0%, #F0F8FF 100%);
            border: 1px solid #BBDEFB;
        }

        .summary-item.administrative .summary-icon {
            color: #2196F3;
        }

        /* 非诉保全审查 - 绿色 */
        .summary-item.security {
            background: linear-gradient(135deg, #E8F5E8 0%, #F0FFF0 100%);
            border: 1px solid #C8E6C9;
        }

        .summary-item.security .summary-icon {
            color: #4CAF50;
        }

        /* 执行案件 - 粉红色 */
        .summary-item.execution {
            background: linear-gradient(135deg, #FFE5E5 0%, #FFF0F0 100%);
            border: 1px solid #FFD1D1;
        }

        .summary-item.execution .summary-icon {
            color: #FF6B6B;
        }

        .summary-item.highlight {
            background: #fff2f2;
            border-color: #ffcdd2;
        }

        /* 图标样式 */
        .summary-icon {
            width: 32px;
            height: 32px;
            margin: 0 auto 8px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.8);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .summary-icon svg {
            width: 20px;
            height: 20px;
        }

        .summary-count {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .summary-count.red {
            color: #f44336;
        }

        .summary-label {
            font-size: 11px;
            color: #666;
        }

        /* 标签栏 */
        .tags-section {
            margin: 0 20px 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            border: 1px solid #bbdefb;
        }

        /* 详情区域 */
        .details-section {
            margin: 20px;
        }

        .details-header {
            color: #4285f4;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .case-item {
            background: #f8f9ff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 15px;
            border: 1px solid #e8eaff;
        }

        .case-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .case-title {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
        }

        .case-icon {
            width: 16px;
            height: 16px;
            background: #4285f4;
            border-radius: 50%;
            margin-right: 6px;
        }

        .case-count {
            color: #666;
            font-size: 12px;
        }

        .case-details {
            background: white;
            border-radius: 6px;
            padding: 12px;
            font-size: 12px;
            line-height: 1.5;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            color: #666;
            min-width: 80px;
        }

        .detail-value {
            color: #333;
            text-align: right;
            flex: 1;
        }

        /* 浮动按钮 */
        .floating-btn {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #4285f4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
            z-index: 1000;
        }

        .floating-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(66, 133, 244, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header">
            <div class="header-time">数据有效期截止时间:2025-06-22 15:15:14</div>
            <button class="get-report-btn">立即获取</button>
            <div class="header-text">
                免查询用户数据隐私，不提供内容给第三方<br>
                已阅读并同意《数据同意书》《个人数据承诺书》<br>
                查询失败系统会自动全额退款
            </div>
            <div class="service-notice">
                本产品提供数据查询服务，没交易不可撤销
            </div>
            <div class="service-desc">
                精准度 专业提供司法数据文书查询服务
            </div>
        </div>

        <!-- 身份验证区域 -->
        <div class="identity-section">
            <div class="identity-header">
                <div class="identity-title">
                    <div class="identity-icon">✓</div>
                    身份证验证
                </div>
                <div class="verify-status">验证通过</div>
            </div>
            <div class="identity-info">
                <div class="info-row">
                    <div class="info-label">姓名</div>
                    <div class="info-value">王哥</div>
                </div>
                <div class="info-row">
                    <div class="info-label">性别</div>
                    <div class="info-value">男</div>
                </div>
                <div class="info-row">
                    <div class="info-label">年龄</div>
                    <div class="info-value">36</div>
                </div>
                <div class="info-row">
                    <div class="info-label">身份证号</div>
                    <div class="info-value">321323*********1212</div>
                </div>
                <div class="info-row">
                    <div class="info-label">归属地</div>
                    <div class="info-value">江苏</div>
                </div>
            </div>
        </div>

        <!-- 信息综述 -->
        <div class="summary-section">
            <div class="summary-header">
                <div class="summary-title">信息综述</div>
                <div class="summary-subtitle">本产品提供数据查询服务，没交易不可撤销</div>
            </div>
            
            <div class="summary-grid">
                <div class="summary-item civil">
                    <div class="summary-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                        </svg>
                    </div>
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">民事案件</div>
                </div>
                <div class="summary-item criminal highlight">
                    <div class="summary-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M11,8C11,9.66 9.66,11 8,11C6.34,11 5,9.66 5,8C5,6.34 6.34,5 8,5C9.66,5 11,6.34 11,8M14.5,19C14.5,18.17 14.33,17.39 14,16.68V17C14,17.55 13.55,18 13,18H3C2.45,18 2,17.55 2,17V16C2,14.89 2.89,14 4,14H12C12.27,14 12.53,14.04 12.78,14.1C13.5,12.27 15.06,11 16.91,11C17.23,11 17.54,11.03 17.84,11.08C17.95,10.74 18,10.38 18,10C18,6.69 15.31,4 12,4C8.69,4 6,6.69 6,10H8C8,7.79 9.79,6 12,6C14.21,6 16,7.79 16,10C16,10.38 15.95,10.74 15.84,11.08C20.63,11.54 24,15.26 24,19.5C24,20.33 23.33,21 22.5,21C21.67,21 21,20.33 21,19.5C21,16.46 18.54,14 15.5,14C12.46,14 10,16.46 10,19.5C10,20.33 9.33,21 8.5,21C7.67,21 7,20.33 7,19.5C7,15.36 10.36,12 14.5,12V19Z"/>
                        </svg>
                    </div>
                    <div class="summary-count red">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">刑事案件</div>
                </div>
                <div class="summary-item administrative">
                    <div class="summary-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                    </div>
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">行政案件</div>
                </div>
                <div class="summary-item security">
                    <div class="summary-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
                        </svg>
                    </div>
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">非诉保全审查</div>
                </div>
                <div class="summary-item execution">
                    <div class="summary-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                        </svg>
                    </div>
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">执行案件</div>
                </div>
            </div>
        </div>

        <!-- 标签栏 -->
        <div class="tags-section">
            <div class="tag">民事案件</div>
            <div class="tag">刑事案件</div>
            <div class="tag">行政案件</div>
            <div class="tag">非诉保全审查</div>
            <div class="tag">执行案件</div>
            <div class="tag">强制清算</div>
        </div>

        <!-- 信息详情 -->
        <div class="details-section">
            <div class="details-header">信息详情</div>
            
            <div class="case-item">
                <div class="case-header">
                    <div class="case-title">
                        <div class="case-icon"></div>
                        民事案件记录
                    </div>
                    <div class="case-count">数量：1</div>
                </div>
                
                <div class="case-details">
                    <div class="detail-row">
                        <div class="detail-label">(2024) 豫1423民初736号</div>
                        <div class="detail-value">
                            <span style="color: #4285f4;">已结案 ∧</span>
                        </div>
                    </div>
                    
                    <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                        <div class="detail-row">
                            <div class="detail-label">案件唯一ID：</div>
                            <div class="detail-value">3bdc26c1e27d54d69dc6fec77dec374b</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">案件类型：</div>
                            <div class="detail-value">民事一审</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">案号：</div>
                            <div class="detail-value">(2024) 豫1423民初736号</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">后续案号：</div>
                            <div class="detail-value">(2024)豫1423执906号 8281e6f11e660eb4781c4d5997dbcaf2</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">案件标识：</div>
                            <div class="detail-value">6fa261c8675f74d09d07f8be6d6888f1</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">经办法院：</div>
                            <div class="detail-value">宁陵县人民法院</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">法院所属等级：</div>
                            <div class="detail-value">基层法院</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">案件审级：</div>
                            <div class="detail-value">一审</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">审理程序：</div>
                            <div class="detail-value">一审</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">所属地域：</div>
                            <div class="detail-value">河南省</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">立案时间：</div>
                            <div class="detail-value">2024-03-06</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">立案案由：</div>
                            <div class="detail-value">合同、准合同纠纷</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">立案案由详细：</div>
                            <div class="detail-value">合同、准合同纠纷,合同纠纷,买卖合同纠纷</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">起诉标的金额等级：</div>
                            <div class="detail-value">-</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">起诉标的金额：</div>
                            <div class="detail-value">40000</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">审理方式信息：</div>
                            <div class="detail-value">1,2024-04-09 10:00:00,本院第一法庭;1,2,2024-04-08 09:10:00,本院第四法庭;1</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">结案时间：</div>
                            <div class="detail-value">2024-04-19</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 浮动按钮 -->
        <div class="floating-btn" onclick="scrollToTop()">
            ↑
        </div>
    </div>

    <script>
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为案件详情添加展开/收起功能
            const caseItems = document.querySelectorAll('.case-item');
            caseItems.forEach(item => {
                const header = item.querySelector('.case-header');
                const details = item.querySelector('.case-details');
                
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    if (details.style.display === 'none') {
                        details.style.display = 'block';
                    } else {
                        details.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
