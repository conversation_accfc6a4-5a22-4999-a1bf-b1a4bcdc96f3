<?php

namespace api;

use think\Queue;
use think\Cache;
use api\GoodCheckApi;
use apifox\ApifoxFactory;

/**
 * 多API查询策略类
 * 根据不同场景选择最优的查询方式
 */
class MultiApiQueryStrategy
{
    /**
     * 查询策略常量
     */
    const STRATEGY_SYNC = 'sync';           // 同步查询
    const STRATEGY_ASYNC = 'async';         // 异步查询
    const STRATEGY_QUEUE = 'queue';         // 队列查询
    const STRATEGY_PARALLEL = 'parallel';   // 并行查询
    
    /**
     * 根据场景自动选择最优策略
     * @param array $queries 查询配置
     * @param array $options 选项配置
     * @return string 推荐策略
     */
    public static function recommendStrategy($queries, $options = [])
    {
        $queryCount = count($queries);
        $isRealTime = $options['real_time'] ?? true;
        $userWaiting = $options['user_waiting'] ?? true;
        $priority = $options['priority'] ?? 'normal';
        
        // 🚀 实时查询场景（用户等待结果）
        if ($isRealTime && $userWaiting) {
            if ($queryCount <= 3) {
                return self::STRATEGY_SYNC;      // 1-3个API：同步查询
            } else {
                return self::STRATEGY_PARALLEL; // 4+个API：并行查询
            }
        }
        
        // ⏰ 后台处理场景（用户不等待）
        if (!$userWaiting) {
            if ($priority === 'high') {
                return self::STRATEGY_ASYNC;     // 高优先级：异步查询
            } else {
                return self::STRATEGY_QUEUE;     // 普通优先级：队列查询
            }
        }
        
        // 🔄 批量处理场景
        if ($queryCount > 10) {
            return self::STRATEGY_QUEUE;         // 大批量：队列查询
        }
        
        return self::STRATEGY_SYNC; // 默认同步查询
    }
    
    /**
     * 同步查询（适合1-3个API，用户等待）
     * @param array $queries 查询配置
     * @return array 查询结果
     */
    public static function syncQuery($queries)
    {
        $results = [];
        $startTime = microtime(true);
        
        foreach ($queries as $key => $query) {
            try {
                $result = self::executeQuery($query);
                $results[$key] = [
                    'success' => true,
                    'data' => $result,
                    'query_time' => microtime(true) - $startTime
                ];
            } catch (\Exception $e) {
                $results[$key] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'query_time' => microtime(true) - $startTime
                ];
            }
        }
        
        return [
            'strategy' => self::STRATEGY_SYNC,
            'total_time' => microtime(true) - $startTime,
            'results' => $results
        ];
    }
    
    /**
     * 并行查询（适合4+个API，用户等待）
     * @param array $queries 查询配置
     * @return array 查询结果
     */
    public static function parallelQuery($queries)
    {
        $results = [];
        $startTime = microtime(true);
        $multiHandle = curl_multi_init();
        $curlHandles = [];
        
        // 初始化所有curl句柄
        foreach ($queries as $key => $query) {
            $ch = self::initCurlHandle($query);
            $curlHandles[$key] = $ch;
            curl_multi_add_handle($multiHandle, $ch);
        }
        
        // 执行并行请求
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
            curl_multi_select($multiHandle);
        } while ($running > 0);
        
        // 收集结果
        foreach ($curlHandles as $key => $ch) {
            $response = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            
            if ($error) {
                $results[$key] = [
                    'success' => false,
                    'error' => $error
                ];
            } else {
                $results[$key] = [
                    'success' => $httpCode === 200,
                    'data' => json_decode($response, true),
                    'http_code' => $httpCode
                ];
            }
            
            curl_multi_remove_handle($multiHandle, $ch);
            curl_close($ch);
        }
        
        curl_multi_close($multiHandle);
        
        return [
            'strategy' => self::STRATEGY_PARALLEL,
            'total_time' => microtime(true) - $startTime,
            'results' => $results
        ];
    }
    
    /**
     * 异步查询（适合高优先级后台处理）
     * @param array $queries 查询配置
     * @param string $callbackUrl 回调地址
     * @return array 任务信息
     */
    public static function asyncQuery($queries, $callbackUrl = '')
    {
        $taskId = 'async_' . uniqid();
        
        // 使用进程或后台任务执行
        $command = "php think api:async-query --task-id={$taskId} --callback={$callbackUrl}";
        
        if (PHP_OS_FAMILY === 'Windows') {
            pclose(popen("start /B {$command}", "r"));
        } else {
            exec("{$command} > /dev/null 2>&1 &");
        }
        
        // 缓存查询配置
        Cache::set("async_query_{$taskId}", $queries, 3600);
        
        return [
            'strategy' => self::STRATEGY_ASYNC,
            'task_id' => $taskId,
            'status' => 'processing',
            'callback_url' => $callbackUrl
        ];
    }
    
    /**
     * 队列查询（适合大批量或低优先级）
     * @param array $queries 查询配置
     * @param array $options 队列选项
     * @return array 队列信息
     */
    public static function queueQuery($queries, $options = [])
    {
        $queueName = $options['queue'] ?? 'api_query';
        $delay = $options['delay'] ?? 0;
        $taskIds = [];
        
        foreach ($queries as $key => $query) {
            $taskId = "queue_{$key}_" . uniqid();
            $taskIds[] = $taskId;
            
            // 添加到队列
            Queue::push('app\\job\\ApiQuery', [
                'task_id' => $taskId,
                'query' => $query,
                'key' => $key
            ], $queueName, $delay);
        }
        
        return [
            'strategy' => self::STRATEGY_QUEUE,
            'queue_name' => $queueName,
            'task_ids' => $taskIds,
            'total_tasks' => count($taskIds)
        ];
    }
    
    /**
     * 执行单个查询
     * @param array $query 查询配置
     * @return mixed 查询结果
     */
    private static function executeQuery($query)
    {
        $type = $query['type'];
        $params = $query['params'];
        
        switch ($type) {
            case 'goodcheck_person':
                $api = new GoodCheckApi($query['app_id'], $query['secret']);
                return $api->checkPerson($params['name'], $params['id_card']);
                
            case 'goodcheck_company':
                $api = new GoodCheckApi($query['app_id'], $query['secret']);
                return $api->checkCompany($params['company_name'], $params['tax_number']);
                
            case 'pincc_marriage':
                return ApifoxFactory::quickMarriageQuery($params['name'], $params['id_card']);
                
            case 'pincc_education':
                return ApifoxFactory::quickEducationQueryV6($params['name'], $params['id_card']);
                
            case 'pincc_credit':
                return ApifoxFactory::quickPersonCreditQuery($params['name'], $params['id_card']);
                
            default:
                throw new \Exception("不支持的查询类型: {$type}");
        }
    }
    
    /**
     * 初始化CURL句柄
     * @param array $query 查询配置
     * @return resource CURL句柄
     */
    private static function initCurlHandle($query)
    {
        // 这里需要根据具体API构建CURL请求
        // 示例实现，实际需要根据API规范调整
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $query['url']);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($query['params']));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $query['token']
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        return $ch;
    }
}
