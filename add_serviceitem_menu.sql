-- 添加服务项目管理菜单的SQL脚本
-- 请在数据库中执行这些SQL语句

-- 1. 添加主菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
VALUES ('file', 0, 'serviceitem', '服务项目管理', 'fa fa-list', '', '服务项目管理（固定项目，只能编辑）', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal');

-- 2. 获取刚插入的主菜单ID（需要手动替换下面的@parent_id）
-- 查询主菜单ID: SELECT id FROM fa_auth_rule WHERE name = 'serviceitem';

-- 3. 添加子菜单（请将@parent_id替换为上面查询到的实际ID）
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
VALUES 
('file', @parent_id, 'serviceitem/index', '服务项目列表', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @parent_id, 'serviceitem/edit', '编辑服务项目', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @parent_id, 'serviceitem/multi', '批量更新', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');

-- 4. 清除缓存（可选，也可以在后台手动清除）
-- DELETE FROM fa_cache WHERE name = '__menu__';

-- 使用示例：
-- 1. 先执行第一条INSERT语句
-- 2. 执行查询语句获取主菜单ID，假设得到ID为123
-- 3. 将下面的@parent_id替换为123，然后执行子菜单插入语句

-- 完整示例（假设主菜单ID为123）：
/*
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
VALUES 
('file', 123, 'serviceitem/index', '服务项目列表', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', 123, 'serviceitem/edit', '编辑服务项目', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', 123, 'serviceitem/multi', '批量更新', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');
*/
