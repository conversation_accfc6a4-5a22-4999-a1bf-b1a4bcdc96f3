-- ----------------------------
-- Table structure for fa_banner
-- ----------------------------
DROP TABLE IF EXISTS `fa_banner`;
CREATE TABLE `fa_banner` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '图片',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '链接',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `weigh` (`weigh`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';

-- ----------------------------
-- Records of fa_banner
-- ----------------------------
INSERT INTO `fa_banner` VALUES ('1', '轮播图1', '/assets/img/banner1.jpg', 'https://www.example.com', '1', 'normal', '1609459200', '1609459200');
INSERT INTO `fa_banner` VALUES ('2', '轮播图2', '/assets/img/banner2.jpg', 'https://www.example.com', '2', 'normal', '1609459200', '1609459200');
INSERT INTO `fa_banner` VALUES ('3', '轮播图3', '/assets/img/banner3.jpg', 'https://www.example.com', '3', 'normal', '1609459200', '1609459200');