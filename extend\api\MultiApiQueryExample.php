<?php
/**
 * 多API查询策略使用示例
 */

require_once 'MultiApiQueryStrategy.php';

use api\MultiApiQueryStrategy;

echo "=== 多API查询策略选择指南 ===\n\n";

// 📋 场景1：用户实时查询（1-3个API）
echo "📋 场景1：用户实时查询个人信息（推荐：同步查询）\n";
echo "---------------------------------------------------\n";

$queries1 = [
    'marriage' => [
        'type' => 'pincc_marriage',
        'params' => ['name' => '张三', 'id_card' => '******************']
    ],
    'education' => [
        'type' => 'pincc_education', 
        'params' => ['name' => '张三', 'id_card' => '******************']
    ],
    'credit' => [
        'type' => 'pincc_credit',
        'params' => ['name' => '张三', 'id_card' => '******************']
    ]
];

$strategy1 = MultiApiQueryStrategy::recommendStrategy($queries1, [
    'real_time' => true,
    'user_waiting' => true
]);

echo "推荐策略: {$strategy1}\n";
echo "原因: 3个API，用户等待结果，同步查询响应快\n";
echo "预计耗时: 3-9秒\n\n";

// 🚀 场景2：用户实时查询（4+个API）
echo "🚀 场景2：用户全面背景调查（推荐：并行查询）\n";
echo "---------------------------------------------------\n";

$queries2 = [
    'marriage' => ['type' => 'pincc_marriage', 'params' => ['name' => '李四', 'id_card' => '******************']],
    'education' => ['type' => 'pincc_education', 'params' => ['name' => '李四', 'id_card' => '******************']],
    'credit' => ['type' => 'pincc_credit', 'params' => ['name' => '李四', 'id_card' => '******************']],
    'goodcheck' => ['type' => 'goodcheck_person', 'params' => ['name' => '李四', 'id_card' => '******************']],
    'vehicle' => ['type' => 'pincc_vehicle', 'params' => ['name' => '李四', 'id_card' => '******************']],
    'employment' => ['type' => 'pincc_employment', 'params' => ['name' => '李四', 'id_card' => '******************']]
];

$strategy2 = MultiApiQueryStrategy::recommendStrategy($queries2, [
    'real_time' => true,
    'user_waiting' => true
]);

echo "推荐策略: {$strategy2}\n";
echo "原因: 6个API，用户等待结果，并行查询节省时间\n";
echo "预计耗时: 3-5秒（并行执行）\n\n";

// ⏰ 场景3：后台批量处理（推荐：队列查询）
echo "⏰ 场景3：夜间批量风控检查（推荐：队列查询）\n";
echo "---------------------------------------------------\n";

$queries3 = [];
for ($i = 1; $i <= 100; $i++) {
    $queries3["user_{$i}"] = [
        'type' => 'goodcheck_person',
        'params' => ['name' => "用户{$i}", 'id_card' => "*****************{$i}"]
    ];
}

$strategy3 = MultiApiQueryStrategy::recommendStrategy($queries3, [
    'real_time' => false,
    'user_waiting' => false,
    'priority' => 'normal'
]);

echo "推荐策略: {$strategy3}\n";
echo "原因: 100个API，后台处理，队列查询稳定可靠\n";
echo "预计耗时: 10-30分钟（队列处理）\n\n";

// 🔥 场景4：紧急风控检查（推荐：异步查询）
echo "🔥 场景4：紧急风控检查（推荐：异步查询）\n";
echo "---------------------------------------------------\n";

$queries4 = [
    'goodcheck' => ['type' => 'goodcheck_person', 'params' => ['name' => '王五', 'id_card' => '******************']],
    'credit' => ['type' => 'pincc_credit', 'params' => ['name' => '王五', 'id_card' => '******************']],
    'marriage' => ['type' => 'pincc_marriage', 'params' => ['name' => '王五', 'id_card' => '******************']]
];

$strategy4 = MultiApiQueryStrategy::recommendStrategy($queries4, [
    'real_time' => false,
    'user_waiting' => false,
    'priority' => 'high'
]);

echo "推荐策略: {$strategy4}\n";
echo "原因: 高优先级，后台处理，异步查询快速响应\n";
echo "预计耗时: 5-15秒（后台执行）\n\n";

// 💡 实际使用示例
echo "💡 实际使用示例\n";
echo "================\n\n";

echo "1. 同步查询示例：\n";
echo "```php\n";
echo "\$result = MultiApiQueryStrategy::syncQuery(\$queries1);\n";
echo "// 直接返回所有结果\n";
echo "```\n\n";

echo "2. 并行查询示例：\n";
echo "```php\n";
echo "\$result = MultiApiQueryStrategy::parallelQuery(\$queries2);\n";
echo "// 并行执行，快速返回\n";
echo "```\n\n";

echo "3. 异步查询示例：\n";
echo "```php\n";
echo "\$task = MultiApiQueryStrategy::asyncQuery(\$queries4, 'http://callback.url');\n";
echo "// 返回任务ID，后台执行\n";
echo "```\n\n";

echo "4. 队列查询示例：\n";
echo "```php\n";
echo "\$queue = MultiApiQueryStrategy::queueQuery(\$queries3, ['queue' => 'batch_check']);\n";
echo "// 添加到队列，稳定处理\n";
echo "```\n\n";

// 📊 性能对比
echo "📊 性能对比表\n";
echo "==============\n";
echo "| 策略     | 适用场景           | API数量 | 响应时间 | 资源占用 | 可靠性 |\n";
echo "|----------|-------------------|---------|----------|----------|--------|\n";
echo "| 同步查询 | 用户等待，少量API  | 1-3个   | 快       | 低       | 高     |\n";
echo "| 并行查询 | 用户等待，多个API  | 4+个    | 很快     | 中       | 高     |\n";
echo "| 异步查询 | 后台处理，高优先级 | 任意    | 中       | 低       | 中     |\n";
echo "| 队列查询 | 后台处理，大批量   | 大量    | 慢       | 很低     | 很高   |\n\n";

// ⚠️ 注意事项
echo "⚠️ 注意事项\n";
echo "============\n";
echo "1. API限流：注意各API的调用频率限制\n";
echo "2. 超时设置：合理设置超时时间，避免长时间等待\n";
echo "3. 错误处理：做好异常处理和重试机制\n";
echo "4. 缓存策略：对相同查询结果进行缓存\n";
echo "5. 监控告警：监控API调用成功率和响应时间\n";
echo "6. 成本控制：根据业务需求选择合适的查询策略\n\n";

// 🎯 最佳实践建议
echo "🎯 最佳实践建议\n";
echo "================\n";
echo "✅ 用户实时查询：优先选择同步或并行查询\n";
echo "✅ 后台批量处理：优先选择队列查询\n";
echo "✅ 紧急处理：选择异步查询\n";
echo "✅ 混合策略：根据具体业务场景灵活选择\n";
echo "✅ 监控优化：持续监控和优化查询性能\n";

echo "\n=== 示例完成 ===\n";

/**
 * 在控制器中的实际使用示例：
 * 
 * // 用户查询个人信息
 * public function userQuery()
 * {
 *     $queries = [
 *         'marriage' => ['type' => 'pincc_marriage', 'params' => $params],
 *         'education' => ['type' => 'pincc_education', 'params' => $params],
 *         'credit' => ['type' => 'pincc_credit', 'params' => $params]
 *     ];
 *     
 *     $strategy = MultiApiQueryStrategy::recommendStrategy($queries, [
 *         'real_time' => true,
 *         'user_waiting' => true
 *     ]);
 *     
 *     switch ($strategy) {
 *         case 'sync':
 *             $result = MultiApiQueryStrategy::syncQuery($queries);
 *             break;
 *         case 'parallel':
 *             $result = MultiApiQueryStrategy::parallelQuery($queries);
 *             break;
 *         default:
 *             $result = MultiApiQueryStrategy::syncQuery($queries);
 *     }
 *     
 *     return json(['code' => 200, 'data' => $result]);
 * }
 * 
 * // 后台批量处理
 * public function batchProcess()
 * {
 *     $queries = $this->buildBatchQueries(); // 构建批量查询
 *     
 *     $result = MultiApiQueryStrategy::queueQuery($queries, [
 *         'queue' => 'batch_check',
 *         'delay' => 0
 *     ]);
 *     
 *     return json(['code' => 200, 'message' => '批量任务已提交', 'data' => $result]);
 * }
 */
