-- 订单表结构
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(50) NOT NULL COMMENT '订单号',
  `trade_no` varchar(50) DEFAULT NULL COMMENT '支付平台交易号',
  `name` varchar(50) NOT NULL COMMENT '用户姓名',
  `id_card` varchar(20) NOT NULL COMMENT '身份证号(脱敏)',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `total_price` decimal(10,2) NOT NULL COMMENT '总价格',
  `selected_items` text COMMENT '选择的单项服务(JSON)',
  `selected_package` varchar(50) COMMENT '选择的套餐ID',
  `query_config` text COMMENT '查询配置(JSON)',
  `pay_url` varchar(500) COMMENT '支付链接',
  `status` enum('pending','paid','completed','failed','refunded') DEFAULT 'pending' COMMENT '订单状态',
  `query_result` longtext COMMENT '查询结果(JSON)',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `pay_time` int(11) DEFAULT NULL COMMENT '支付时间',
  `complete_time` int(11) DEFAULT NULL COMMENT '完成时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `trade_no` (`trade_no`),
  KEY `status` (`status`),
  KEY `create_time` (`create_time`),
  KEY `phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单状态说明：
-- pending: 待支付 - 订单已创建，等待用户支付
-- paid: 已支付 - 用户已完成支付，等待执行查询
-- completed: 已完成 - 查询已完成，订单结束
-- failed: 失败 - 支付失败或查询失败
-- refunded: 已退款 - 订单已退款

-- 示例数据（可选）
INSERT INTO `orders` (`order_id`, `name`, `id_card`, `phone`, `total_price`, `selected_items`, `selected_package`, `status`, `create_time`) VALUES
('ORDER_20250623001', '张三', '420101****0001', '138****8888', 35.80, NULL, 'basic_report', 'pending', UNIX_TIMESTAMP()),
('ORDER_20250623002', '李四', '420101****0002', '139****9999', 27.40, '[\"1\",\"3\",\"5\"]', NULL, 'paid', UNIX_TIMESTAMP());
