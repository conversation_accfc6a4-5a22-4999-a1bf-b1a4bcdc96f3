<?php

namespace api;

use api\config\ApiConfig;

/**
 * API工厂类
 * 用于创建和管理各种API实例
 */
class ApiFactory
{
    /**
     * 创建精准查涉案API实例
     * @param string $appId 可选，如果不传则使用配置文件中的值
     * @param string $secret 可选，如果不传则使用配置文件中的值
     * @return GoodCheckApi
     */
    public static function createGoodCheckApi($appId = null, $secret = null)
    {
        $config = ApiConfig::getGoodCheckConfig();
        
        $appId = $appId ?: $config['app_id'];
        $secret = $secret ?: $config['secret'];
        
        return new GoodCheckApi($appId, $secret);
    }
    
    /**
     * 快速个人涉案查询
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $options 可选参数
     * @return array
     */
    public static function quickCheckPerson($name, $idCard, $options = [])
    {
        $api = self::createGoodCheckApi();
        
        $orderno = isset($options['orderno']) ? $options['orderno'] : '';
        $clientIp = isset($options['client_ip']) ? $options['client_ip'] : '';
        $interfaceCoding = isset($options['interfacecoding']) ? $options['interfacecoding'] : '';
        
        return $api->checkPerson($name, $idCard, $orderno, $clientIp, $interfaceCoding);
    }
    
    /**
     * 快速企业涉案查询
     * @param string $companyName 企业名称
     * @param string $taxNumber 企业税号
     * @param array $options 可选参数
     * @return array
     */
    public static function quickCheckCompany($companyName, $taxNumber, $options = [])
    {
        $api = self::createGoodCheckApi();
        
        $orderno = isset($options['orderno']) ? $options['orderno'] : '';
        $clientIp = isset($options['client_ip']) ? $options['client_ip'] : '';
        $interfaceCoding = isset($options['interfacecoding']) ? $options['interfacecoding'] : '';
        
        return $api->checkCompany($companyName, $taxNumber, $orderno, $clientIp, $interfaceCoding);
    }
}
