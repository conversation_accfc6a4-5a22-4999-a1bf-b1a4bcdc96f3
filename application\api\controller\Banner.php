<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Banner as BannerModel;

/**
 * 轮播图接口
 */
class Banner extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    
    /**
     * 获取轮播图列表
     *
     * @ApiTitle    (获取轮播图列表)
     * @ApiSummary  (获取所有可见的轮播图)
     * @ApiMethod   (GET)
     * @ApiParams   (无)
     * @ApiReturn   ({"code":1,"msg":"","data":[{"id":1,"title":"轮播图标题","image":"图片路径","url":"链接地址","weigh":1,"status":"normal","createtime":1234567890,"updatetime":1234567890}]})
     */
    public function index()
    {
        $bannerList = BannerModel::where('status', 'normal')
            ->order('weigh', 'desc')
            ->select();
        $this->success('', $bannerList);
    }
    
    /**
     * 获取单个轮播图
     *
     * @ApiTitle    (获取单个轮播图)
     * @ApiSummary  (根据ID获取轮播图详情)
     * @ApiMethod   (GET)
     * @ApiParams   (name="id", type="integer", required=true, description="轮播图ID")
     * @ApiReturn   ({"code":1,"msg":"","data":{"id":1,"title":"轮播图标题","image":"图片路径","url":"链接地址","weigh":1,"status":"normal","createtime":1234567890,"updatetime":1234567890}})
     */
    public function detail($id = null)
    {
        if (!$id) {
            $this->error(__('Parameter %s can not be empty', 'id'));
        }
        $banner = BannerModel::where('status', 'normal')->find($id);
        if (!$banner) {
            $this->error(__('Banner not found'));
        }
        $this->success('', $banner);
    }
}