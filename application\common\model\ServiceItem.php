<?php

namespace app\common\model;

use think\Model;

/**
 * 服务项目模型（固定的单项服务）
 */
class ServiceItem extends Model
{
    // 表名
    protected $name = 'service_item';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'status_text'
    ];

    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return [
            'normal' => '正常',
            'hidden' => '隐藏'
        ];
    }

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : $data['status'];
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取所有可用的服务项目
     */
    public static function getAvailableItems()
    {
        return self::where('status', 'normal')
            ->order('weigh', 'desc')
            ->order('id', 'asc')
            ->select();
    }

    /**
     * 获取服务项目的键值对（用于套餐选择）
     */
    public static function getItemOptions()
    {
        $items = self::getAvailableItems();
        $options = [];
        foreach ($items as $item) {
            $options[$item['id']] = $item['name'];
        }
        return $options;
    }
}
