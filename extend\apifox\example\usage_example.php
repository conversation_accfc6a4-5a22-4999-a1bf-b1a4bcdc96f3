<?php

/**
 * 聘查查API使用示例
 */

// 引入自动加载（如果使用Composer）或手动引入文件
require_once '../PinccApi.php';
require_once '../config/ApifoxConfig.php';
require_once '../ApifoxFactory.php';

use apifox\PinccApi;
use apifox\ApifoxFactory;

// 示例1：直接使用PinccApi类
echo "=== 示例1：直接使用PinccApi类 ===\n";

$secretKey = 'your_secret_key'; // 替换为实际的密钥

$api = new PinccApi($secretKey);

// 全国婚姻查询
$result = $api->marriageQuery('张三', '420101199001010001');
echo "婚姻查询结果：\n";
print_r($result);

// 学历查询（星耀版）
$result = $api->educationQueryV6('李四', '420101199002020002');
echo "学历查询（星耀版）结果：\n";
print_r($result);

// 学历查询（至尊版）
$result = $api->educationQueryV4('王五', '******************');
echo "学历查询（至尊版）结果：\n";
print_r($result);

echo "\n";

// 示例2：使用ApifoxFactory工厂类
echo "=== 示例2：使用ApifoxFactory工厂类 ===\n";

// 快速婚姻查询
$result = ApifoxFactory::quickMarriageQuery('赵六', '******************');
echo "快速婚姻查询结果：\n";
print_r($result);

// 快速个人失信查询
$result = ApifoxFactory::quickPersonCreditQuery('钱七', '******************');
echo "快速个人失信查询结果：\n";
print_r($result);

// 快速企业失信查询
$result = ApifoxFactory::quickCompanyCreditQuery('某某科技有限公司', '91110000123456789X');
echo "快速企业失信查询结果：\n";
print_r($result);

echo "\n";

// 示例3：车辆相关查询
echo "=== 示例3：车辆相关查询 ===\n";

// 个人名下车辆查询（ETC开户人）
$result = ApifoxFactory::quickPersonalVehicleQuery('******************', '1', '孙八');
echo "个人名下车辆查询（ETC开户人）结果：\n";
print_r($result);

// 个人名下车辆查询（车辆所有人）
$result = ApifoxFactory::quickPersonalVehicleQuery('******************', '2');
echo "个人名下车辆查询（车辆所有人）结果：\n";
print_r($result);

echo "\n";

// 示例4：任职和资质查询
echo "=== 示例4：任职和资质查询 ===\n";

// 人企任职记录查询
$result = ApifoxFactory::quickEnterpriseRecordQuery('******************');
echo "人企任职记录查询结果：\n";
print_r($result);

// 个人综合资质评分
$result = ApifoxFactory::quickQualifyScoreQuery('周九', '420101199009090009', '13800138000');
echo "个人综合资质评分结果：\n";
print_r($result);

echo "\n";

// 示例5：错误处理
echo "=== 示例5：错误处理示例 ===\n";

$result = ApifoxFactory::quickMarriageQuery('', ''); // 传入空参数测试错误处理

if ($result['success']) {
    echo "查询成功：\n";
    print_r($result['data']);
} else {
    echo "查询失败：" . $result['message'] . "\n";
    if ($result['data']) {
        echo "原始响应：" . $result['data'] . "\n";
    }
}

/**
 * 在ThinkPHP控制器中的使用示例：
 * 
 * <?php
 * namespace app\index\controller;
 * 
 * use think\Controller;
 * use apifox\ApifoxFactory;
 * 
 * class PersonCheck extends Controller
 * {
 *     // 婚姻状况查询
 *     public function marriageCheck()
 *     {
 *         $name = $this->request->param('name');
 *         $cardNo = $this->request->param('card_no');
 *         
 *         if (empty($name) || empty($cardNo)) {
 *             return json(['code' => 400, 'msg' => '参数不能为空']);
 *         }
 *         
 *         $result = ApifoxFactory::quickMarriageQuery($name, $cardNo);
 *         
 *         if ($result['success']) {
 *             return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
 *         } else {
 *             return json(['code' => 500, 'msg' => $result['message']]);
 *         }
 *     }
 *     
 *     // 学历查询
 *     public function educationCheck()
 *     {
 *         $fullName = $this->request->param('full_name');
 *         $idCardNo = $this->request->param('id_card_no');
 *         $version = $this->request->param('version', 'v6'); // v6星耀版，v4至尊版
 *         
 *         if (empty($fullName) || empty($idCardNo)) {
 *             return json(['code' => 400, 'msg' => '参数不能为空']);
 *         }
 *         
 *         if ($version === 'v4') {
 *             $result = ApifoxFactory::quickEducationQueryV4($fullName, $idCardNo);
 *         } else {
 *             $result = ApifoxFactory::quickEducationQueryV6($fullName, $idCardNo);
 *         }
 *         
 *         if ($result['success']) {
 *             return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
 *         } else {
 *             return json(['code' => 500, 'msg' => $result['message']]);
 *         }
 *     }
 *     
 *     // 失信查询
 *     public function creditCheck()
 *     {
 *         $entityName = $this->request->param('entity_name');
 *         $entityNo = $this->request->param('entity_no');
 *         $entityType = $this->request->param('entity_type', 1); // 1个人，2企业
 *         
 *         if (empty($entityName) || empty($entityNo)) {
 *             return json(['code' => 400, 'msg' => '参数不能为空']);
 *         }
 *         
 *         if ($entityType == 2) {
 *             $result = ApifoxFactory::quickCompanyCreditQuery($entityName, $entityNo);
 *         } else {
 *             $result = ApifoxFactory::quickPersonCreditQuery($entityName, $entityNo);
 *         }
 *         
 *         if ($result['success']) {
 *             return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
 *         } else {
 *             return json(['code' => 500, 'msg' => $result['message']]);
 *         }
 *     }
 *     
 *     // 车辆查询
 *     public function vehicleCheck()
 *     {
 *         $idCardNo = $this->request->param('id_card_no');
 *         $userType = $this->request->param('user_type', '2'); // 1-ETC开户人；2-车辆所有人；3-ETC经办人
 *         $fullName = $this->request->param('full_name', '');
 *         
 *         if (empty($idCardNo)) {
 *             return json(['code' => 400, 'msg' => '身份证号不能为空']);
 *         }
 *         
 *         $result = ApifoxFactory::quickPersonalVehicleQuery($idCardNo, $userType, $fullName);
 *         
 *         if ($result['success']) {
 *             return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
 *         } else {
 *             return json(['code' => 500, 'msg' => $result['message']]);
 *         }
 *     }
 *     
 *     // 任职记录查询
 *     public function enterpriseRecordCheck()
 *     {
 *         $idCardNo = $this->request->param('id_card_no');
 *         
 *         if (empty($idCardNo)) {
 *             return json(['code' => 400, 'msg' => '身份证号不能为空']);
 *         }
 *         
 *         $result = ApifoxFactory::quickEnterpriseRecordQuery($idCardNo);
 *         
 *         if ($result['success']) {
 *             return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
 *         } else {
 *             return json(['code' => 500, 'msg' => $result['message']]);
 *         }
 *     }
 *     
 *     // 综合资质评分
 *     public function qualifyScoreCheck()
 *     {
 *         $fullName = $this->request->param('full_name');
 *         $idCardNo = $this->request->param('id_card_no');
 *         $mobile = $this->request->param('mobile');
 *         
 *         if (empty($fullName) || empty($idCardNo) || empty($mobile)) {
 *             return json(['code' => 400, 'msg' => '参数不能为空']);
 *         }
 *         
 *         $result = ApifoxFactory::quickQualifyScoreQuery($fullName, $idCardNo, $mobile);
 *         
 *         if ($result['success']) {
 *             return json(['code' => 200, 'msg' => '查询成功', 'data' => $result['data']]);
 *         } else {
 *             return json(['code' => 500, 'msg' => $result['message']]);
 *         }
 *     }
 * }
 */
