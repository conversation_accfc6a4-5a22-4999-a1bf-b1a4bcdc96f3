-- 添加轮播图管理菜单
INSERT INTO `fa_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(0, 'banner', '轮播图管理', 'fa fa-image', '', '轮播图管理菜单', 1, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal');

-- 获取刚插入的父级ID
SET @pid = LAST_INSERT_ID();

-- 添加子菜单
INSERT INTO `fa_auth_rule` (`pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(@pid, 'banner/index', '查看', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal'),
(@pid, 'banner/add', '添加', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal'),
(@pid, 'banner/edit', '编辑', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal'),
(@pid, 'banner/del', '删除', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal'),
(@pid, 'banner/multi', '批量更新', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal'),
(@pid, 'banner/recyclebin', '回收站', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal'),
(@pid, 'banner/restore', '还原', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal'),
(@pid, 'banner/destroy', '真实删除', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal'),
(@pid, 'banner/import', '导入', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal'),
(@pid, 'banner/export', '导出', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(NOW()), UNIX_TIMESTAMP(NOW()), 0, 'normal');