<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库对接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .section h3 { margin-top: 0; color: #333; }
        .item { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        .item-header { font-weight: bold; color: #007bff; }
        .item-details { margin-top: 5px; font-size: 14px; color: #666; }
        .price { color: #e91e63; font-weight: bold; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #e8f5e8; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .loading { background: #e3f2fd; color: #1976d2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据库对接测试</h1>
        
        <!-- 测试按钮 -->
        <div class="section">
            <h3>测试功能</h3>
            <button class="btn" onclick="testServiceItems()">测试单项服务加载</button>
            <button class="btn" onclick="testPackages()">测试套餐加载</button>
            <button class="btn" onclick="testQueryConfig()">测试查询配置构建</button>
            <button class="btn" onclick="testFullQuery()">测试完整查询流程</button>
        </div>
        
        <!-- 单项服务测试结果 -->
        <div class="section">
            <h3>📋 单项服务列表</h3>
            <div id="serviceItemsResult">点击"测试单项服务加载"按钮查看结果</div>
        </div>
        
        <!-- 套餐测试结果 -->
        <div class="section">
            <h3>📦 套餐列表</h3>
            <div id="packagesResult">点击"测试套餐加载"按钮查看结果</div>
        </div>
        
        <!-- 查询配置测试结果 -->
        <div class="section">
            <h3>⚙️ 查询配置测试</h3>
            <div id="queryConfigResult">点击"测试查询配置构建"按钮查看结果</div>
        </div>
        
        <!-- 完整查询测试结果 -->
        <div class="section">
            <h3>🚀 完整查询测试</h3>
            <div id="fullQueryResult">点击"测试完整查询流程"按钮查看结果</div>
        </div>
    </div>

    <script>
        // 测试单项服务加载
        function testServiceItems() {
            const resultDiv = document.getElementById('serviceItemsResult');
            resultDiv.innerHTML = '<div class="result loading">正在加载单项服务...</div>';
            
            fetch('/index/index')
                .then(response => response.text())
                .then(html => {
                    // 解析HTML中的单项服务数据
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const singleItems = doc.querySelectorAll('#single-content .single-item');
                    
                    if (singleItems.length > 0) {
                        let html = '<div class="result">✅ 成功加载 ' + singleItems.length + ' 个单项服务：</div>';
                        
                        singleItems.forEach(item => {
                            const id = item.getAttribute('data-id');
                            const price = item.getAttribute('data-price');
                            const name = item.querySelector('h4').textContent;
                            const desc = item.querySelector('p').textContent;
                            
                            html += `
                                <div class="item">
                                    <div class="item-header">ID: ${id} - ${name}</div>
                                    <div class="item-details">
                                        描述：${desc}<br>
                                        价格：<span class="price">¥${price}</span>
                                    </div>
                                </div>
                            `;
                        });
                        
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = '<div class="result error">❌ 未找到单项服务数据</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="result error">❌ 加载失败：' + error.message + '</div>';
                });
        }
        
        // 测试套餐加载
        function testPackages() {
            const resultDiv = document.getElementById('packagesResult');
            resultDiv.innerHTML = '<div class="result loading">正在加载套餐...</div>';
            
            fetch('/index/index')
                .then(response => response.text())
                .then(html => {
                    // 解析HTML中的套餐数据
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const packageItems = doc.querySelectorAll('#package-content .module-item');
                    
                    if (packageItems.length > 0) {
                        let html = '<div class="result">✅ 成功加载 ' + packageItems.length + ' 个套餐：</div>';
                        
                        packageItems.forEach(item => {
                            const id = item.getAttribute('data-id');
                            const price = item.getAttribute('data-price');
                            const name = item.querySelector('.module-name').textContent;
                            const tags = item.querySelectorAll('.module-tag');
                            
                            let tagList = [];
                            tags.forEach(tag => tagList.push(tag.textContent));
                            
                            html += `
                                <div class="item">
                                    <div class="item-header">ID: ${id} - ${name}</div>
                                    <div class="item-details">
                                        价格：<span class="price">¥${price}</span><br>
                                        包含服务：${tagList.join(', ')}
                                    </div>
                                </div>
                            `;
                        });
                        
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = '<div class="result error">❌ 未找到套餐数据</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="result error">❌ 加载失败：' + error.message + '</div>';
                });
        }
        
        // 测试查询配置构建
        function testQueryConfig() {
            const resultDiv = document.getElementById('queryConfigResult');
            resultDiv.innerHTML = '<div class="result loading">正在测试查询配置...</div>';
            
            // 测试不同的配置场景
            const testCases = [
                {
                    name: '单项选择测试',
                    params: {
                        name: '张三',
                        id_card: '******************',
                        selected_items: ['1', '2'] // 假设的服务项目ID
                    }
                },
                {
                    name: '套餐选择测试',
                    params: {
                        name: '李四',
                        id_card: '******************',
                        selected_package: '1' // 假设的套餐ID
                    }
                },
                {
                    name: '默认配置测试',
                    params: {
                        name: '王五',
                        id_card: '******************'
                    }
                }
            ];
            
            let html = '';
            let completedTests = 0;
            
            testCases.forEach((testCase, index) => {
                const queryString = new URLSearchParams(testCase.params).toString();
                
                fetch(`/index/query?${queryString}`)
                    .then(response => response.json())
                    .then(data => {
                        completedTests++;
                        
                        if (data.success) {
                            html += `
                                <div class="item">
                                    <div class="item-header">✅ ${testCase.name}</div>
                                    <div class="item-details">
                                        查询类型：${data.query_info.query_config.type}<br>
                                        API数量：${data.query_info.query_config.total_apis || Object.keys(data.query_info.query_config.apis).length}<br>
                                        总价格：<span class="price">¥${data.query_info.query_config.total_price}</span><br>
                                        包含API：${Object.values(data.query_info.query_config.apis).map(api => api.name).join(', ')}
                                    </div>
                                </div>
                            `;
                        } else {
                            html += `
                                <div class="item">
                                    <div class="item-header">❌ ${testCase.name}</div>
                                    <div class="item-details error">错误：${data.message}</div>
                                </div>
                            `;
                        }
                        
                        if (completedTests === testCases.length) {
                            resultDiv.innerHTML = '<div class="result">配置测试完成：</div>' + html;
                        }
                    })
                    .catch(error => {
                        completedTests++;
                        html += `
                            <div class="item">
                                <div class="item-header">❌ ${testCase.name}</div>
                                <div class="item-details error">请求失败：${error.message}</div>
                            </div>
                        `;
                        
                        if (completedTests === testCases.length) {
                            resultDiv.innerHTML = '<div class="result">配置测试完成：</div>' + html;
                        }
                    });
            });
        }
        
        // 测试完整查询流程
        function testFullQuery() {
            const resultDiv = document.getElementById('fullQueryResult');
            resultDiv.innerHTML = '<div class="result loading">正在测试完整查询流程...</div>';
            
            // 使用默认参数进行完整查询测试
            const params = {
                name: '测试用户',
                id_card: '******************',
                strategy: 'sync' // 使用同步查询避免复杂性
            };
            
            const queryString = new URLSearchParams(params).toString();
            
            fetch(`/index/query?${queryString}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = '<div class="result">✅ 完整查询流程测试成功：</div>';
                        
                        html += `
                            <div class="item">
                                <div class="item-header">查询信息</div>
                                <div class="item-details">
                                    姓名：${data.query_info.name}<br>
                                    身份证：${data.query_info.id_card}<br>
                                    查询时间：${data.query_info.query_time}<br>
                                    总耗时：${data.query_info.total_time}<br>
                                    总价格：<span class="price">¥${data.query_info.query_config.total_price}</span>
                                </div>
                            </div>
                        `;
                        
                        if (data.results && data.results.results) {
                            html += '<div class="item"><div class="item-header">API查询结果</div><div class="item-details">';
                            
                            Object.entries(data.results.results).forEach(([key, result]) => {
                                const status = result.success ? '✅' : '❌';
                                html += `${status} ${result.api_name}: ${result.message}<br>`;
                            });
                            
                            html += '</div></div>';
                        }
                        
                        if (data.summary) {
                            html += `
                                <div class="item">
                                    <div class="item-header">风险摘要</div>
                                    <div class="item-details">
                                        风险等级：${data.summary.risk_level}<br>
                                        成功API：${data.summary.success_count}/${data.summary.total_apis}<br>
                                        风险因素：${data.summary.risk_factors.join(', ') || '无'}<br>
                                        建议：${data.summary.recommendations.join(', ') || '无'}
                                    </div>
                                </div>
                            `;
                        }
                        
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = '<div class="result error">❌ 查询失败：' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="result error">❌ 请求失败：' + error.message + '</div>';
                });
        }
    </script>
</body>
</html>
