<svg width="400" height="200" viewBox="0 0 400 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<!-- 背景渐变 -->
<defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgba(255,255,255,0.05);stop-opacity:1" />
    </linearGradient>
</defs>

<!-- 几何装饰元素 -->
<circle cx="320" cy="60" r="40" fill="rgba(255,255,255,0.1)"/>
<circle cx="350" cy="120" r="25" fill="rgba(255,255,255,0.08)"/>
<rect x="280" y="140" width="60" height="40" rx="8" fill="rgba(255,255,255,0.06)"/>

<!-- 文档图标 -->
<rect x="300" y="40" width="40" height="50" rx="4" fill="rgba(255,255,255,0.15)"/>
<rect x="305" y="50" width="30" height="2" fill="rgba(255,255,255,0.3)"/>
<rect x="305" y="55" width="25" height="2" fill="rgba(255,255,255,0.3)"/>
<rect x="305" y="60" width="20" height="2" fill="rgba(255,255,255,0.3)"/>

<!-- 检查标记 -->
<circle cx="330" cy="30" r="12" fill="rgba(255,255,255,0.2)"/>
<path d="M325 30L328 33L335 26" stroke="rgba(255,255,255,0.6)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
