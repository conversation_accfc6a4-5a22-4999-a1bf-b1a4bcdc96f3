<div class="banner-container new-banner-style">
    <div id="banner-carousel" class="carousel slide" data-ride="carousel">
        <!-- 指示器 -->
        <ol class="carousel-indicators">
            {foreach name="bannerList" item="vo" key="k"}
            <li data-target="#banner-carousel" data-slide-to="{$k}" {if $k==0}class="active"{/if}></li>
            {/foreach}
        </ol>
        <!-- 轮播图片 -->
        <div class="carousel-inner" role="listbox">
            {foreach name="bannerList" item="vo" key="k"}
            <div class="item {if $k==0}active{/if}">
                <a href="{$vo.url}" target="_blank">
                    <div class="banner-bg">
                        <img src="{$vo.image}" alt="{$vo.title}" class="banner-img">
                        <div class="carousel-caption new-caption">
                            <div class="banner-badge">更实惠</div>
                            <h3 class="banner-title">入职背调报告<br>企业采购入口</h3>
                            <a href="{$vo.url}" class="banner-btn">立即联系</a>
                        </div>
                    </div>
                </a>
            </div>
            {/foreach}
        </div>
        <!-- 左右切换按钮 -->
        <a class="left carousel-control" href="#banner-carousel" role="button" data-slide="prev">
            <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
            <span class="sr-only">上一个</span>
        </a>
        <a class="right carousel-control" href="#banner-carousel" role="button" data-slide="next">
            <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
            <span class="sr-only">下一个</span>
        </a>
    </div>
</div>
<!-- 引入触摸轮播插件 -->
<link rel="stylesheet" href="__CDN__/assets/libs/bootstrap-touch-carousel/bootstrap-touch-carousel.min.css">
<script src="__CDN__/assets/libs/bootstrap-touch-carousel/bootstrap-touch-carousel.min.js"></script>
<script>
    $(function () {
        // 初始化轮播图
        $('#banner-carousel').carousel({
            interval: 5000, // 自动播放间隔，单位毫秒
            pause: 'hover', // 鼠标悬停时暂停
            wrap: true      // 循环播放
        });
    });
</script>
<style>
.banner-container.new-banner-style {
    margin-bottom: 20px;
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 102, 255, 0.08);
    background: #eaf4ff;
}
.banner-bg {
    background: linear-gradient(90deg, #4faaff 0%, #eaf4ff 100%);
    border-radius: 18px;
    position: relative;
    min-height: 160px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.banner-img {
    width: 120px;
    height: 120px;
    object-fit: contain;
    margin-left: 20px;
    background: transparent;
}
.carousel-caption.new-caption {
    position: absolute;
    left: 160px;
    top: 30px;
    text-align: left;
    background: none;
    color: #222;
    padding: 0;
}
.banner-badge {
    display: inline-block;
    background: #ff6b00;
    color: #fff;
    font-size: 14px;
    border-radius: 8px;
    padding: 2px 10px;
    margin-bottom: 8px;
}
.banner-title {
    font-size: 22px;
    font-weight: bold;
    color: #222;
    margin-bottom: 12px;
    line-height: 1.3;
}
.banner-btn {
    display: inline-block;
    background: #fff;
    color: #ff6b00;
    font-weight: bold;
    border-radius: 20px;
    padding: 6px 22px;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(255,107,0,0.08);
    text-decoration: none;
    border: 2px solid #ff6b00;
    transition: background 0.2s, color 0.2s;
}
.banner-btn:hover {
    background: #ff6b00;
    color: #fff;
}
@media (max-width: 768px) {
    .carousel-caption.new-caption {
        left: 110px;
        top: 18px;
    }
    .banner-title {
        font-size: 16px;
    }
    .banner-img {
        width: 80px;
        height: 80px;
        margin-left: 10px;
    }
}
</style>