<?php

namespace app\common\model;

use think\Model;

/**
 * 套餐模型
 */
class Package extends Model
{
    // 表名
    protected $name = 'package';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'status_text',
        'type_text'
    ];

    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return [
            'normal' => '正常',
            'hidden' => '隐藏'
        ];
    }

    /**
     * 获取类型列表
     */
    public function getTypeList()
    {
        return [
            'single' => '单项',
            'package' => '套餐'
        ];
    }

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : $data['status'];
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 类型文本获取器
     */
    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['type'];
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 标签设置器
     */
    public function setTagsAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    /**
     * 标签获取器
     */
    public function getTagsAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }

    /**
     * 获取正常状态的套餐列表
     */
    public static function getNormalList($type = '')
    {
        $where = ['status' => 'normal'];
        if ($type) {
            $where['type'] = $type;
        }
        
        return self::where($where)
            ->order('weigh', 'desc')
            ->order('id', 'asc')
            ->select();
    }

    /**
     * 获取单项列表
     */
    public static function getSingleList()
    {
        return self::getNormalList('single');
    }

    /**
     * 获取套餐列表
     */
    public static function getPackageList()
    {
        return self::getNormalList('package');
    }
}
