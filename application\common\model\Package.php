<?php

namespace app\common\model;

use think\Model;

/**
 * 套餐模型（只管理套餐，不管理单项）
 */
class Package extends Model
{
    // 表名
    protected $name = 'package';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'status_text',
        'service_items_list'
    ];

    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return [
            'normal' => '正常',
            'hidden' => '隐藏'
        ];
    }

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : $data['status'];
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 服务项目设置器
     */
    public function setServiceItemsAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    /**
     * 服务项目获取器
     */
    public function getServiceItemsAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }

    /**
     * 服务项目列表获取器（获取完整的服务项目信息）
     */
    public function getServiceItemsListAttr($value, $data)
    {
        if (empty($data['service_items'])) {
            return [];
        }

        $itemIds = explode(',', $data['service_items']);
        return ServiceItem::where('id', 'in', $itemIds)
            ->where('status', 'normal')
            ->order('weigh', 'desc')
            ->select();
    }

    /**
     * 获取正常状态的套餐列表
     */
    public static function getPackageList()
    {
        return self::where('status', 'normal')
            ->order('weigh', 'desc')
            ->order('id', 'asc')
            ->select();
    }

    /**
     * 关联服务项目模型
     */
    public function serviceItems()
    {
        return $this->belongsToMany('ServiceItem', 'package_service_item', 'service_item_id', 'package_id');
    }
}
