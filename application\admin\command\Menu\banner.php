<?php

namespace app\admin\command\Menu;

use app\admin\command\Menu;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;

class Banner extends Menu
{
    protected function configure()
    {
        parent::configure();
        $this->setName('menu:banner')
            ->setDescription('Create banner menu');
    }

    protected function execute(Input $input, Output $output)
    {
        $menus = [
            [
                'name'    => 'banner',
                'title'   => '轮播图管理',
                'icon'    => 'fa fa-image',
                'remark'  => '用于管理网站首页的轮播图',
                'sublist' => [
                    ['name' => 'banner/index', 'title' => '查看'],
                    ['name' => 'banner/add', 'title' => '添加'],
                    ['name' => 'banner/edit', 'title' => '修改'],
                    ['name' => 'banner/del', 'title' => '删除'],
                    ['name' => 'banner/multi', 'title' => '批量更新'],
                ],
            ]
        ];
        
        $this->installMenu($menus);
        $output->info('Banner menu created successfully');
    }
}