<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>法律数据查询</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(90deg, #4e6ef2, #618eff);
            color: white;
            padding: 15px 20px;
            position: relative;
        }
        
        .header-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .expiry-time {
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .disclaimer {
            font-size: 12px;
            line-height: 1.5;
            margin-bottom: 12px;
        }
        
        .small-text {
            font-size: 12px;
            color: #e8eaed;
            margin-bottom: 5px;
        }
        
        .warning-text {
            background-color: #fff2f0;
            color: #ff4d4f;
            padding: 8px 10px;
            font-size: 12px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .service-text {
            font-size: 13px;
            color: white;
            margin-top: 15px;
        }
        
        .get-now-btn {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background-color: white;
            color: #4e6ef2;
            border: none;
            padding: 8px 18px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .id-verify-container {
            background: white;
            border-radius: 10px;
            max-width: 600px;
            margin: 15px auto;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .id-verify-title {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .id-verify-title .icon {
            color: #4e6ef2;
            margin-right: 5px;
            font-size: 18px;
        }
        
        .verification-status {
            position: absolute;
            right: 20px;
            top: 15px;
            background-color: #4e6ef2;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
        }
        
        .id-info-table {
            width: 100%;
            position: relative;
            z-index: 1;
        }
        
        .id-info-table td {
            padding: 8px 0;
            font-size: 14px;
        }
        
        .id-info-table td:first-child {
            width: 80px;
            text-align: left;
            color: #666;
        }
        
        .id-info-table td:last-child {
            text-align: right;
            font-weight: normal;
        }
        
        .info-summary {
            max-width: 600px;
            margin: 20px auto;
        }
        
        .info-title {
            font-size: 16px;
            color: #4e6ef2;
            margin-bottom: 10px;
            padding-left: 10px;
            border-left: 3px solid #4e6ef2;
        }
        
        .info-subtitle {
            font-size: 12px;
            color: #999;
            text-align: right;
        }
        
        .case-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 10px;
        }
        
        .case-card {
            background: #e9efff;
            padding: 15px 10px;
            border-radius: 8px;
            text-align: center;
            position: relative;
        }
        
        .case-title {
            font-size: 14px;
            color: #333;
        }
        
        .case-count {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 5px;
        }

        .floating-button {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background-color: #4e6ef2;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            color: white;
            font-size: 20px;
        }

        .back-to-top {
            position: absolute;
            bottom: 70px;
            right: 20px;
            width: 40px;
            height: 40px;
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            color: #4e6ef2;
            font-size: 20px;
        }

        .bottom-nav {
            width: 100%;
            background-color: white;
            border-top: 1px solid #eee;
            padding: 8px 0;
            font-size: 12px;
            display: flex;
            justify-content: space-around;
            color: #666;
            margin-top: 15px;
        }

        .bottom-nav a {
            color: #666;
            text-decoration: none;
        }
        
        .bottom-nav .active {
            color: #4e6ef2;
        }

        .circle-bg {
            position: absolute;
            right: -20px;
            bottom: -20px;
            width: 150px;
            height: 150px;
            background-color: rgba(78, 110, 242, 0.1);
            border-radius: 50%;
            z-index: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="expiry-time">数据有效期截止时间:2023-06-22 15:42:33</div>
            <div class="disclaimer">免责声明:大数据查询，不能作为终审判定</div>
            <div class="small-text">已阅读并同意《授权同意书》及《他人授权承诺书》</div>
            <div class="small-text">查询失败系统会自动全额退款</div>
            <div class="warning-text">本产品提供数据查询服务，该交易不可撤销</div>
            <div class="service-text">精准查 专业提供司法数据文书咨询服务</div>
        </div>
        <button class="get-now-btn">立即获取</button>
    </div>
    
    <div class="id-verify-container">
        <div class="circle-bg"></div>
        <div class="id-verify-title">
            <span class="icon">✓</span>
            身份证验证
        </div>
        <div class="verification-status">验证通过</div>
        
        <table class="id-info-table">
            <tr>
                <td>姓名</td>
                <td>王哥</td>
            </tr>
            <tr>
                <td>性别</td>
                <td>男</td>
            </tr>
            <tr>
                <td>年龄</td>
                <td>36</td>
            </tr>
            <tr>
                <td>身份证号</td>
                <td>321323**********1212</td>
            </tr>
            <tr>
                <td>归属地</td>
                <td>江苏</td>
            </tr>
        </table>
    </div>
    
    <div class="info-summary">
        <div class="info-title">信息综述</div>
        <div class="info-subtitle">本产品提供数据查询服务，该交易不可撤销</div>
        
        <div class="case-grid">
            <div class="case-card">
                <div class="case-title">民事案件</div>
                <div class="case-count">1件</div>
            </div>
            <div class="case-card">
                <div class="case-title">刑事案件</div>
                <div class="case-count">1件</div>
            </div>
            <div class="case-card">
                <div class="case-title">行政案件</div>
                <div class="case-count">1件</div>
            </div>
            <div class="case-card">
                <div class="case-title">非诉保全审查</div>
                <div class="case-count">1件</div>
            </div>
            <div class="case-card">
                <div class="case-title">执行案件</div>
                <div class="case-count">1件</div>
            </div>
            <div class="case-card">
                <div class="case-title">强制清算与破产案件</div>
                <div class="case-count">1件</div>
            </div>
            <div class="case-card">
                <div class="case-title">管辖案件</div>
                <div class="case-count">1件</div>
            </div>
            <div class="case-card">
                <div class="case-title">赔偿案件</div>
                <div class="case-count">1件</div>
            </div>
        </div>
    </div>
    
    <div class="info-summary" style="position: relative;">
        <div class="floating-button">
            <span>💬</span>
        </div>
        
        <div class="back-to-top">
            <span>↑</span>
        </div>
    </div>
    
    <div class="info-summary">
        <div class="bottom-nav">
            <a href="#" class="active">民事案件</a>
            <a href="#">刑事案件</a>
            <a href="#">行政案件</a>
            <a href="#">非诉保全审查</a>
            <a href="#">执行案件</a>
        </div>
    </div>
</body>
</html>
