<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>征信报告示例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(180deg, #4A90E2 0%, #7B68EE 100%);
            min-height: 100vh;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: transparent;
            min-height: 100vh;
            position: relative;
            padding: 0;
        }

        /* 头部区域 */
        .header {
            background: transparent;
            color: white;
            padding: 15px 20px;
            position: relative;
        }

        .header-time {
            font-size: 11px;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .header-text {
            font-size: 11px;
            line-height: 1.4;
            margin-bottom: 12px;
            opacity: 0.95;
        }

        .get-report-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.4);
            color: white;
            padding: 6px 14px;
            border-radius: 15px;
            font-size: 11px;
            cursor: pointer;
        }

        .service-notice {
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 10px;
            border-radius: 12px;
            font-size: 11px;
            margin-bottom: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: inline-block;
        }

        .service-desc {
            font-size: 11px;
            opacity: 0.9;
        }

        /* 身份验证区域 */
        .identity-section {
            background: white;
            margin: 15px 20px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .identity-header {
            background: linear-gradient(90deg, #E8F4FD 0%, #F0F8FF 100%);
            padding: 10px 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .identity-title {
            display: flex;
            align-items: center;
            font-size: 13px;
            font-weight: 500;
            color: #333;
        }

        .identity-icon {
            width: 18px;
            height: 18px;
            background: #4A90E2;
            border-radius: 50%;
            margin-right: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }

        .verify-status {
            background: linear-gradient(90deg, #4A90E2 0%, #5BA0F2 100%);
            color: white;
            padding: 3px 10px;
            border-radius: 10px;
            font-size: 11px;
        }

        .identity-info {
            padding: 12px 15px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 6px 0;
            border-bottom: 1px solid #f8f8f8;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #666;
            font-size: 13px;
        }

        .info-value {
            color: #333;
            font-size: 13px;
            font-weight: 500;
        }

        /* 信息综述区域 */
        .summary-section {
            margin: 15px 20px;
        }

        .summary-header {
            display: flex;
            align-items: baseline;
            margin-bottom: 12px;
        }

        .summary-title {
            color: #4A90E2;
            font-size: 15px;
            font-weight: 600;
            margin-right: 8px;
        }

        .summary-subtitle {
            color: #999;
            font-size: 10px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 15px;
        }

        .summary-item {
            background: rgba(248, 249, 255, 0.9);
            padding: 8px 6px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid rgba(232, 234, 255, 0.8);
            position: relative;
        }

        .summary-item.highlight {
            background: rgba(255, 242, 242, 0.9);
            border-color: rgba(255, 205, 210, 0.8);
        }

        .summary-count {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 2px;
        }

        .summary-count.red {
            color: #f44336;
        }

        .summary-label {
            font-size: 9px;
            color: #666;
            line-height: 1.2;
        }

        /* 标签栏 */
        .tags-section {
            margin: 0 20px 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .tag {
            background: rgba(227, 242, 253, 0.9);
            color: #1976d2;
            padding: 3px 6px;
            border-radius: 10px;
            font-size: 10px;
            border: 1px solid rgba(187, 222, 251, 0.8);
        }

        /* 详情区域 */
        .details-section {
            margin: 15px 20px;
        }

        .details-header {
            color: #4A90E2;
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .case-item {
            background: rgba(248, 249, 255, 0.9);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            border: 1px solid rgba(232, 234, 255, 0.8);
        }

        .case-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .case-title {
            display: flex;
            align-items: center;
            font-size: 13px;
            font-weight: 500;
        }

        .case-icon {
            width: 14px;
            height: 14px;
            background: #4A90E2;
            border-radius: 50%;
            margin-right: 5px;
        }

        .case-count {
            color: #666;
            font-size: 11px;
        }

        .case-details {
            background: white;
            border-radius: 6px;
            padding: 10px;
            font-size: 11px;
            line-height: 1.4;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            color: #666;
            min-width: 70px;
        }

        .detail-value {
            color: #333;
            text-align: right;
            flex: 1;
        }

        /* 浮动按钮 */
        .floating-btn {
            position: fixed;
            bottom: 60px;
            right: 15px;
            width: 40px;
            height: 40px;
            background: #4A90E2;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 3px 8px rgba(74, 144, 226, 0.3);
            z-index: 1000;
        }

        .floating-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header">
            <div class="header-time">数据有效期截止时间:2025-06-22 15:23:50</div>
            <button class="get-report-btn">立即获取</button>
            <div class="header-text">
                免查询用户数据隐私，不提供内容给第三方<br>
                已阅读并同意《数据同意书》《个人数据权承诺书》<br>
                查询失败系统会自动全额退款
            </div>
            <div class="service-notice">
                本产品提供数据查询服务，没交易不可撤销
            </div>
            <div class="service-desc">
                精准度 专业提供司法数据文书查询服务
            </div>
        </div>

        <!-- 身份验证区域 -->
        <div class="identity-section">
            <div class="identity-header">
                <div class="identity-title">
                    <div class="identity-icon">✓</div>
                    身份证验证
                </div>
                <div class="verify-status">验证通过</div>
            </div>
            <div class="identity-info">
                <div class="info-row">
                    <div class="info-label">姓名</div>
                    <div class="info-value">王哥</div>
                </div>
                <div class="info-row">
                    <div class="info-label">性别</div>
                    <div class="info-value">男</div>
                </div>
                <div class="info-row">
                    <div class="info-label">年龄</div>
                    <div class="info-value">36</div>
                </div>
                <div class="info-row">
                    <div class="info-label">身份证号</div>
                    <div class="info-value">321323*********1212</div>
                </div>
                <div class="info-row">
                    <div class="info-label">归属地</div>
                    <div class="info-value">江苏</div>
                </div>
            </div>
        </div>

        <!-- 信息综述 -->
        <div class="summary-section">
            <div class="summary-header">
                <div class="summary-title">信息综述</div>
                <div class="summary-subtitle">本产品提供数据查询服务，没交易不可撤销</div>
            </div>
            
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">民事案件</div>
                </div>
                <div class="summary-item highlight">
                    <div class="summary-count red">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">刑事案件</div>
                </div>
                <div class="summary-item">
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">行政案件</div>
                </div>
                <div class="summary-item">
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">非诉保全审查</div>
                </div>
                <div class="summary-item">
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">执行案件</div>
                </div>
                <div class="summary-item">
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">强制清算与破产案件</div>
                </div>
                <div class="summary-item">
                    <div class="summary-count">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">管辖案件</div>
                </div>
                <div class="summary-item highlight">
                    <div class="summary-count red">1</div>
                    <div class="summary-label">件</div>
                    <div class="summary-label">赔偿案件</div>
                </div>
            </div>
        </div>

        <!-- 标签栏 -->
        <div class="tags-section">
            <div class="tag">民事案件</div>
            <div class="tag">刑事案件</div>
            <div class="tag">行政案件</div>
            <div class="tag">非诉保全审查</div>
            <div class="tag">执行案件</div>
            <div class="tag">强制清算</div>
        </div>

        <!-- 信息详情 -->
        <div class="details-section">
            <div class="details-header">信息详情</div>
            
            <div class="case-item">
                <div class="case-header">
                    <div class="case-title">
                        <div class="case-icon"></div>
                        民事案件记录
                    </div>
                    <div class="case-count">数量：1</div>
                </div>
                
                <div class="case-details">
                    <div class="detail-row">
                        <div class="detail-label">(2024) 豫1423民初736号</div>
                        <div class="detail-value">
                            <span style="color: #4285f4;">已结案 ∧</span>
                        </div>
                    </div>
                    
                    <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                        <div class="detail-row">
                            <div class="detail-label">案件唯一ID：</div>
                            <div class="detail-value">3bdc26c1e27d54d69dc6fec77dec374b</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">案件类型：</div>
                            <div class="detail-value">民事一审</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">案号：</div>
                            <div class="detail-value">(2024) 豫1423民初736号</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">后续案号：</div>
                            <div class="detail-value">(2024)豫1423执906号 8281e6f11e660eb4781c4d5997dbcaf2</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">案件标识：</div>
                            <div class="detail-value">6fa261c8675f74d09d07f8be6d6888f1</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">经办法院：</div>
                            <div class="detail-value">宁陵县人民法院</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">法院所属等级：</div>
                            <div class="detail-value">基层法院</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">案件审级：</div>
                            <div class="detail-value">一审</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">审理程序：</div>
                            <div class="detail-value">一审</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">所属地域：</div>
                            <div class="detail-value">河南省</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">立案时间：</div>
                            <div class="detail-value">2024-03-06</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">立案案由：</div>
                            <div class="detail-value">合同、准合同纠纷</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">立案案由详细：</div>
                            <div class="detail-value">合同、准合同纠纷,合同纠纷,买卖合同纠纷</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">起诉标的金额等级：</div>
                            <div class="detail-value">-</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">起诉标的金额：</div>
                            <div class="detail-value">40000</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">审理方式信息：</div>
                            <div class="detail-value">1,2024-04-09 10:00:00,本院第一法庭;1,2,2024-04-08 09:10:00,本院第四法庭;1</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">结案时间：</div>
                            <div class="detail-value">2024-04-19</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 浮动按钮 -->
        <div class="floating-btn" onclick="scrollToTop()">
            ↑
        </div>
    </div>

    <script>
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为案件详情添加展开/收起功能
            const caseItems = document.querySelectorAll('.case-item');
            caseItems.forEach(item => {
                const header = item.querySelector('.case-header');
                const details = item.querySelector('.case-details');
                
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    if (details.style.display === 'none') {
                        details.style.display = 'block';
                    } else {
                        details.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
