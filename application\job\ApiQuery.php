<?php

namespace app\job;

use think\queue\Job;
use think\Cache;
use think\Log;
use api\GoodCheckApi;
use apifox\ApifoxFactory;

/**
 * API查询队列任务
 */
class ApiQuery
{
    /**
     * 执行队列任务
     * @param Job $job 任务对象
     * @param array $data 任务数据
     */
    public function fire(Job $job, $data)
    {
        $taskId = $data['task_id'];
        $query = $data['query'];
        $key = $data['key'];
        
        try {
            Log::info("开始执行API查询任务: {$taskId}");
            
            // 执行查询
            $result = $this->executeQuery($query);
            
            // 保存结果到缓存
            $cacheKey = "api_result_{$taskId}";
            Cache::set($cacheKey, [
                'success' => true,
                'data' => $result,
                'completed_at' => date('Y-m-d H:i:s'),
                'task_id' => $taskId,
                'key' => $key
            ], 3600);
            
            Log::info("API查询任务完成: {$taskId}");
            
            // 删除任务
            $job->delete();
            
        } catch (\Exception $e) {
            Log::error("API查询任务失败: {$taskId}, 错误: " . $e->getMessage());
            
            // 保存错误结果
            $cacheKey = "api_result_{$taskId}";
            Cache::set($cacheKey, [
                'success' => false,
                'error' => $e->getMessage(),
                'failed_at' => date('Y-m-d H:i:s'),
                'task_id' => $taskId,
                'key' => $key
            ], 3600);
            
            // 重试逻辑
            if ($job->attempts() < 3) {
                Log::info("API查询任务重试: {$taskId}, 尝试次数: " . $job->attempts());
                $job->release(60); // 60秒后重试
            } else {
                Log::error("API查询任务最终失败: {$taskId}");
                $job->delete();
            }
        }
    }
    
    /**
     * 执行具体的API查询
     * @param array $query 查询配置
     * @return mixed 查询结果
     */
    private function executeQuery($query)
    {
        $type = $query['type'];
        $params = $query['params'];
        
        switch ($type) {
            case 'goodcheck_person':
                $api = new GoodCheckApi($query['app_id'], $query['secret']);
                return $api->checkPerson($params['name'], $params['id_card']);
                
            case 'goodcheck_company':
                $api = new GoodCheckApi($query['app_id'], $query['secret']);
                return $api->checkCompany($params['company_name'], $params['tax_number']);
                
            case 'pincc_marriage':
                return ApifoxFactory::quickMarriageQuery($params['name'], $params['id_card']);
                
            case 'pincc_education':
                return ApifoxFactory::quickEducationQueryV6($params['name'], $params['id_card']);
                
            case 'pincc_credit':
                return ApifoxFactory::quickPersonCreditQuery($params['name'], $params['id_card']);
                
            default:
                throw new \Exception("不支持的查询类型: {$type}");
        }
    }
}
