<?php

namespace app\job;

use think\queue\Job;
use think\Cache;
use think\Log;
use api\GoodCheckApi;
use apifox\ApifoxFactory;

/**
 * 多API联合查询队列任务
 */
class MultiApiQuery
{
    /**
     * 执行多API联合查询队列任务
     * @param Job $job 任务对象
     * @param array $data 任务数据
     */
    public function fire(Job $job, $data)
    {
        $mainTaskId = $data['main_task_id'];
        $taskData = $data['task_data'];
        $callbackUrl = $data['callback_url'] ?? '';
        $taskId = $taskData['task_id'];

        $startTime = microtime(true);

        try {
            Log::info("开始执行多API查询任务: {$taskId}");

            // 执行具体的API查询
            $result = $this->executeApiQuery($taskData);

            $queryTime = round(microtime(true) - $startTime, 2);

            // 保存单个任务结果到缓存
            $resultData = [
                'success' => true,
                'task_id' => $taskId,
                'main_task_id' => $mainTaskId,
                'api_name' => $taskData['api_name'],
                'type' => $taskData['type'],
                'data' => $result,
                'query_time' => $queryTime . '秒',
                'completed_at' => date('Y-m-d H:i:s'),
                'message' => '查询成功'
            ];

            Cache::set("queue_result_{$taskId}", $resultData, 3600);

            Log::info("多API查询任务完成: {$taskId}, 耗时: {$queryTime}秒");

            // 检查是否所有子任务都完成了
            $this->checkMainTaskCompletion($mainTaskId, $callbackUrl);

            // 删除任务
            $job->delete();

        } catch (\Exception $e) {
            $queryTime = round(microtime(true) - $startTime, 2);

            Log::error("多API查询任务失败: {$taskId}, 错误: " . $e->getMessage());

            // 保存错误结果
            $errorData = [
                'success' => false,
                'task_id' => $taskId,
                'main_task_id' => $mainTaskId,
                'api_name' => $taskData['api_name'],
                'type' => $taskData['type'],
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'query_time' => $queryTime . '秒',
                'failed_at' => date('Y-m-d H:i:s'),
                'message' => '查询失败'
            ];

            Cache::set("queue_result_{$taskId}", $errorData, 3600);

            // 重试逻辑
            if ($job->attempts() < 3) {
                Log::info("多API查询任务重试: {$taskId}, 尝试次数: " . $job->attempts());
                $job->release(60); // 60秒后重试
            } else {
                Log::error("多API查询任务最终失败: {$taskId}");
                $this->checkMainTaskCompletion($mainTaskId, $callbackUrl);
                $job->delete();
            }
        }
    }
    
    /**
     * 执行具体的API查询
     * @param array $taskData 任务数据
     * @return mixed 查询结果
     */
    private function executeApiQuery($taskData)
    {
        $type = $taskData['type'];
        $params = $taskData['params'];

        switch ($type) {
            case 'goodcheck':
                // 精准查涉案API
                $api = new GoodCheckApi($params['app_id'], $params['secret']);
                $queryParams = [
                    'orderno' => 'QUEUE_' . time() . '_' . mt_rand(1000, 9999),
                    'name' => $params['name'],
                    'code' => $params['id_card'],
                    'querytype' => '1',
                    'detail' => '1',
                    'client_ip' => '127.0.0.1' // 队列任务使用服务器IP
                ];
                return $api->goodCheck($queryParams);

            case 'pincc_marriage':
                // 婚姻状况查询
                return ApifoxFactory::quickMarriageQuery($params['name'], $params['id_card']);

            case 'pincc_education':
                // 学历信息查询
                return ApifoxFactory::quickEducationQueryV6($params['name'], $params['id_card']);

            case 'pincc_credit':
                // 个人失信查询
                return ApifoxFactory::quickPersonCreditQuery($params['name'], $params['id_card']);

            default:
                throw new \Exception("不支持的查询类型: {$type}");
        }
    }

    /**
     * 检查主任务是否完成
     * @param string $mainTaskId 主任务ID
     * @param string $callbackUrl 回调地址
     */
    private function checkMainTaskCompletion($mainTaskId, $callbackUrl = '')
    {
        try {
            // 获取主任务信息
            $taskInfo = Cache::get("queue_task_{$mainTaskId}");
            if (!$taskInfo) {
                return;
            }

            // 检查所有子任务是否完成
            $allCompleted = true;
            $results = [];

            foreach ($taskInfo['queued_tasks'] as $task) {
                $result = Cache::get("queue_result_{$task['task_id']}");
                if ($result) {
                    $results[] = $result;
                } else {
                    $allCompleted = false;
                    break;
                }
            }

            // 如果所有任务都完成了
            if ($allCompleted) {
                Log::info("主任务完成: {$mainTaskId}");

                // 更新主任务状态
                $taskInfo['status'] = 'completed';
                $taskInfo['completed_at'] = date('Y-m-d H:i:s');
                $taskInfo['results'] = $results;

                Cache::set("queue_task_{$mainTaskId}", $taskInfo, 3600);

                // 如果有回调地址，发送回调通知
                if (!empty($callbackUrl)) {
                    $this->sendCallback($callbackUrl, $taskInfo);
                }
            }

        } catch (\Exception $e) {
            Log::error("检查主任务完成状态失败: {$mainTaskId}, 错误: " . $e->getMessage());
        }
    }

    /**
     * 发送回调通知
     * @param string $callbackUrl 回调地址
     * @param array $taskInfo 任务信息
     */
    private function sendCallback($callbackUrl, $taskInfo)
    {
        try {
            $postData = json_encode([
                'task_id' => $taskInfo['main_task_id'],
                'status' => $taskInfo['status'],
                'completed_at' => $taskInfo['completed_at'],
                'results' => $taskInfo['results']
            ]);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $callbackUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($postData)
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                Log::info("回调通知发送成功: {$callbackUrl}");
            } else {
                Log::warning("回调通知发送失败: {$callbackUrl}, HTTP状态码: {$httpCode}");
            }

        } catch (\Exception $e) {
            Log::error("发送回调通知异常: {$callbackUrl}, 错误: " . $e->getMessage());
        }
    }
}
