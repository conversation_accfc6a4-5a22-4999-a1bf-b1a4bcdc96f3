<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .section h3 { margin-top: 0; color: #333; }
        .form-group { margin: 10px 0; }
        .form-group label { display: inline-block; width: 100px; font-weight: bold; }
        .form-group input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-secondary { background: #6c757d; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .order-item { margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .order-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .order-id { font-weight: bold; color: #007bff; }
        .order-status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-paid { background: #d4edda; color: #155724; }
        .status-completed { background: #cce5ff; color: #004085; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .order-details { font-size: 14px; color: #666; }
        .order-details div { margin: 5px 0; }
        .json-display { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 订单管理测试</h1>
        
        <!-- 订单查询 -->
        <div class="section">
            <h3>🔍 订单查询</h3>
            <div class="form-group">
                <label>订单号：</label>
                <input type="text" id="queryOrderId" placeholder="输入订单号">
                <button class="btn" onclick="queryByOrderId()">查询</button>
            </div>
            <div class="form-group">
                <label>手机号：</label>
                <input type="text" id="queryPhone" placeholder="输入手机号">
                <button class="btn" onclick="queryByPhone()">查询</button>
            </div>
            <div class="form-group">
                <button class="btn btn-secondary" onclick="clearResults()">清空结果</button>
            </div>
        </div>
        
        <!-- 订单详情查询 -->
        <div class="section">
            <h3>📄 订单详情</h3>
            <div class="form-group">
                <label>订单号：</label>
                <input type="text" id="detailOrderId" placeholder="输入订单号">
                <button class="btn btn-success" onclick="getOrderDetail()">获取详情</button>
            </div>
        </div>
        
        <!-- 支付状态测试 -->
        <div class="section">
            <h3>💰 支付状态测试</h3>
            <div class="form-group">
                <label>订单号：</label>
                <input type="text" id="paymentOrderId" placeholder="输入订单号">
                <button class="btn btn-danger" onclick="testPaymentStatus()">测试支付状态</button>
            </div>
        </div>
        
        <!-- 查询结果 -->
        <div class="section">
            <h3>📊 查询结果</h3>
            <div id="queryResults">暂无查询结果</div>
        </div>
        
        <!-- 订单详情结果 -->
        <div class="section">
            <h3>📋 订单详情</h3>
            <div id="orderDetail">暂无订单详情</div>
        </div>
    </div>

    <script>
        // 根据订单号查询
        function queryByOrderId() {
            const orderId = document.getElementById('queryOrderId').value.trim();
            if (!orderId) {
                alert('请输入订单号');
                return;
            }
            
            queryOrders({ order_id: orderId });
        }
        
        // 根据手机号查询
        function queryByPhone() {
            const phone = document.getElementById('queryPhone').value.trim();
            if (!phone) {
                alert('请输入手机号');
                return;
            }
            
            queryOrders({ phone: phone });
        }
        
        // 查询订单
        function queryOrders(params) {
            const queryString = new URLSearchParams(params).toString();
            
            fetch(`/index/orderQuery?${queryString}`)
                .then(response => response.json())
                .then(data => {
                    displayQueryResults(data);
                })
                .catch(error => {
                    displayError('查询请求失败：' + error.message);
                });
        }
        
        // 显示查询结果
        function displayQueryResults(data) {
            const container = document.getElementById('queryResults');
            
            if (data.success) {
                let html = `<div class="result success">✅ 查询成功，找到 ${data.data.length} 个订单：</div>`;
                
                data.data.forEach(order => {
                    html += `
                        <div class="order-item">
                            <div class="order-header">
                                <div class="order-id">${order.order_id}</div>
                                <div class="order-status status-${order.status}">${order.status_text}</div>
                            </div>
                            <div class="order-details">
                                <div><strong>用户：</strong>${order.name} (${order.phone})</div>
                                <div><strong>金额：</strong>¥${order.total_price}</div>
                                <div><strong>创建时间：</strong>${order.create_time_text}</div>
                                <div><strong>支付时间：</strong>${order.pay_time_text || '未支付'}</div>
                                <div><strong>完成时间：</strong>${order.complete_time_text || '未完成'}</div>
                                ${order.trade_no ? `<div><strong>交易号：</strong>${order.trade_no}</div>` : ''}
                                ${order.error_message ? `<div><strong>错误信息：</strong>${order.error_message}</div>` : ''}
                            </div>
                        </div>
                    `;
                });
                
                container.innerHTML = html;
            } else {
                container.innerHTML = `<div class="result error">❌ ${data.message}</div>`;
            }
        }
        
        // 获取订单详情
        function getOrderDetail() {
            const orderId = document.getElementById('detailOrderId').value.trim();
            if (!orderId) {
                alert('请输入订单号');
                return;
            }
            
            fetch(`/index/orderDetail?order_id=${orderId}`)
                .then(response => response.json())
                .then(data => {
                    displayOrderDetail(data);
                })
                .catch(error => {
                    displayDetailError('详情查询失败：' + error.message);
                });
        }
        
        // 显示订单详情
        function displayOrderDetail(data) {
            const container = document.getElementById('orderDetail');
            
            if (data.success) {
                const order = data.data;
                let html = `
                    <div class="result success">✅ 订单详情获取成功：</div>
                    <div class="order-item">
                        <div class="order-header">
                            <div class="order-id">${order.order_id}</div>
                            <div class="order-status status-${order.status}">${order.status_text}</div>
                        </div>
                        <div class="order-details">
                            <div><strong>用户信息：</strong>${order.name} | ${order.id_card} | ${order.phone}</div>
                            <div><strong>金额：</strong>¥${order.total_price}</div>
                            <div><strong>创建时间：</strong>${order.create_time_text}</div>
                            <div><strong>支付时间：</strong>${order.pay_time_text || '未支付'}</div>
                            <div><strong>完成时间：</strong>${order.complete_time_text || '未完成'}</div>
                            ${order.trade_no ? `<div><strong>交易号：</strong>${order.trade_no}</div>` : ''}
                            ${order.pay_url ? `<div><strong>支付链接：</strong><a href="${order.pay_url}" target="_blank">查看</a></div>` : ''}
                            ${order.error_message ? `<div><strong>错误信息：</strong>${order.error_message}</div>` : ''}
                        </div>
                `;
                
                // 显示选择的服务项目
                if (order.selected_items_array) {
                    html += `
                        <div style="margin-top: 10px;">
                            <strong>选择的单项服务：</strong>
                            <div class="json-display">${JSON.stringify(order.selected_items_array, null, 2)}</div>
                        </div>
                    `;
                }
                
                if (order.selected_package) {
                    html += `<div><strong>选择的套餐：</strong>${order.selected_package}</div>`;
                }
                
                // 显示查询配置
                if (order.query_config_array) {
                    html += `
                        <div style="margin-top: 10px;">
                            <strong>查询配置：</strong>
                            <div class="json-display">${JSON.stringify(order.query_config_array, null, 2)}</div>
                        </div>
                    `;
                }
                
                // 显示查询结果
                if (order.query_result_array) {
                    html += `
                        <div style="margin-top: 10px;">
                            <strong>查询结果：</strong>
                            <div class="json-display">${JSON.stringify(order.query_result_array, null, 2)}</div>
                        </div>
                    `;
                }
                
                html += '</div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = `<div class="result error">❌ ${data.message}</div>`;
            }
        }
        
        // 测试支付状态
        function testPaymentStatus() {
            const orderId = document.getElementById('paymentOrderId').value.trim();
            if (!orderId) {
                alert('请输入订单号');
                return;
            }
            
            // 这里可以调用支付状态验证接口
            fetch(`/index/query?payment_order_id=${orderId}&name=测试&id_card=******************`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayError('✅ 支付验证通过，查询执行成功');
                    } else {
                        displayError('❌ 支付验证失败：' + data.message);
                    }
                })
                .catch(error => {
                    displayError('支付状态测试失败：' + error.message);
                });
        }
        
        // 显示错误信息
        function displayError(message) {
            document.getElementById('queryResults').innerHTML = `<div class="result error">${message}</div>`;
        }
        
        // 显示详情错误信息
        function displayDetailError(message) {
            document.getElementById('orderDetail').innerHTML = `<div class="result error">${message}</div>`;
        }
        
        // 清空结果
        function clearResults() {
            document.getElementById('queryResults').innerHTML = '暂无查询结果';
            document.getElementById('orderDetail').innerHTML = '暂无订单详情';
        }
    </script>
</body>
</html>
