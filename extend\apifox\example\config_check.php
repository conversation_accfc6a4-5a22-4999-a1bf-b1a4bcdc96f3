<?php
/**
 * API配置检查示例
 * 用于验证API配置是否正确
 */

require_once '../config/ApifoxConfig.php';

use apifox\config\ApifoxConfig;

echo "=== 聘查查API配置检查 ===\n\n";

// 1. 显示当前配置
echo "1. 当前配置信息：\n";
echo "APP ID: " . ApifoxConfig::getAppId() . "\n";
echo "密钥: " . (ApifoxConfig::getSecret() === 'your_secret_key' ? '未配置' : '已配置') . "\n";
echo "API地址: " . ApifoxConfig::getBaseUrl() . "\n";
echo "超时时间: " . ApifoxConfig::getTimeout() . "秒\n";
echo "调试模式: " . (ApifoxConfig::isDebug() ? '开启' : '关闭') . "\n\n";

// 2. 验证配置
echo "2. 配置验证结果：\n";
$validation = ApifoxConfig::validateConfig();

if ($validation['valid']) {
    echo "✅ 配置验证通过，可以正常使用API\n";
} else {
    echo "❌ 配置验证失败，请检查以下问题：\n";
    foreach ($validation['errors'] as $error) {
        echo "   - " . $error . "\n";
    }
    echo "\n";
    echo "📝 配置说明：\n";
    echo "   请编辑 extend/apifox/config/ApifoxConfig.php 文件\n";
    echo "   将 'your_app_id' 替换为实际的APP ID\n";
    echo "   将 'your_secret_key' 替换为实际的密钥\n";
}

echo "\n";

// 3. 环境变量检查
echo "3. 环境变量检查：\n";
$envVars = [
    'PINCC_APP_ID' => getenv('PINCC_APP_ID'),
    'PINCC_SECRET' => getenv('PINCC_SECRET'),
    'PINCC_BASE_URL' => getenv('PINCC_BASE_URL'),
    'PINCC_TIMEOUT' => getenv('PINCC_TIMEOUT'),
    'PINCC_DEBUG' => getenv('PINCC_DEBUG'),
];

$hasEnvVars = false;
foreach ($envVars as $key => $value) {
    if ($value !== false) {
        echo "✅ {$key}: " . ($key === 'PINCC_SECRET' ? '已设置' : $value) . "\n";
        $hasEnvVars = true;
    }
}

if (!$hasEnvVars) {
    echo "ℹ️  未检测到环境变量配置\n";
    echo "   如需使用环境变量，请在 .env 文件中设置：\n";
    echo "   PINCC_APP_ID=your_actual_app_id\n";
    echo "   PINCC_SECRET=your_actual_secret_key\n";
}

echo "\n=== 检查完成 ===\n";
