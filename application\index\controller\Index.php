<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Banner;
use app\common\model\Package;
use app\common\model\ServiceItem;
use api\GoodCheckApi;
use apifox\ApifoxFactory;
use think\Queue;
use think\Cache;

class Index extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        // 获取轮播图数据
        $bannerList = Banner::where('status', 'normal')
            ->order('weigh', 'desc')
            ->select();

        // 判断是否有轮播图数据
        $hasBanner = count($bannerList);

        // 获取固定的服务项目（单项）
        $singleList = ServiceItem::getAvailableItems();

        // 获取套餐数据
        $packageList = Package::getPackageList();

        $this->view->assign([
            'bannerList' => $bannerList,
            'hasBanner' => $hasBanner,
            'singleList' => $singleList,
            'packageList' => $packageList
        ]);
        return $this->view->fetch();
    }

    /**
     * 联合查询信息（API + Apifox）
     */
    public function query()
    {
        $site = config('site');

        // 获取查询参数
        $name = $this->request->param('name', '刘建汉');
        $idCard = $this->request->param('id_card', '51310119881207121X');

        // 获取用户选择的查询项目
        $selectedItems = $this->request->param('selected_items', []); // 前端选择的单项
        $selectedPackage = $this->request->param('selected_package', ''); // 前端选择的套餐

        // 根据选择构建查询配置
        $queryConfig = $this->buildQueryConfig($selectedItems, $selectedPackage);

        try {
            $results = [];
            $startTime = microtime(true);

            // 根据查询策略选择执行方式
            $strategy = $this->request->param('strategy', 'auto'); // auto, sync, parallel, queue

            switch ($strategy) {
                case 'queue':
                    // 队列查询（推荐用于批量处理）
                    return $this->queueQueryByConfig($name, $idCard, $site, $queryConfig);

                case 'parallel':
                    // 并行查询（推荐用于多个API实时查询）
                    $results = $this->parallelQueryByConfig($name, $idCard, $site, $queryConfig);
                    break;

                case 'sync':
                    // 同步查询（推荐用于少量API）
                    $results = $this->syncQueryByConfig($name, $idCard, $site, $queryConfig);
                    break;

                case 'auto':
                default:
                    // 自动选择策略（根据API数量）
                    $apiCount = count($queryConfig['apis']);
                    if ($apiCount >= 4) {
                        $results = $this->parallelQueryByConfig($name, $idCard, $site, $queryConfig);
                    } else {
                        $results = $this->syncQueryByConfig($name, $idCard, $site, $queryConfig);
                    }
                    break;
            }

            $totalTime = microtime(true) - $startTime;

            // 返回联合查询结果
            $response = [
                'success' => true,
                'message' => '联合查询完成',
                'query_info' => [
                    'name' => $name,
                    'id_card' => $this->maskIdCard($idCard),
                    'query_time' => date('Y-m-d H:i:s'),
                    'total_time' => round($totalTime, 2) . '秒',
                    'selected_items' => $selectedItems,
                    'selected_package' => $selectedPackage,
                    'query_config' => $queryConfig
                ],
                'results' => $results,
                'summary' => $this->generateSummary($results)
            ];

            // 如果是AJAX请求，返回JSON
            if ($this->request->isAjax()) {
                return json($response);
            }

            // 否则显示页面
            $this->view->assign('queryResult', $response);
            return $this->view->fetch('query_result');

        } catch (\Exception $e) {
            $error = [
                'success' => false,
                'message' => '查询失败：' . $e->getMessage(),
                'error_code' => $e->getCode()
            ];

            if ($this->request->isAjax()) {
                return json($error);
            }

            $this->error($error['message']);
        }
    }

    /**
     * 精准查涉案API查询
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $site 站点配置
     * @return array 查询结果
     */
    private function queryGoodCheck($name, $idCard, $site)
    {
        $startTime = microtime(true);

        try {
            // 使用配置文件中的密钥
            $api = new GoodCheckApi($site['jc_appid'], $site['jc_secret']);

            $params = [
                'orderno' => 'ORDER_' . time() . '_' . mt_rand(1000, 9999),
                'name' => $name,
                'code' => $idCard,
                'querytype' => '1', // 1=个人查询
                'detail' => '1',    // 1=详细信息
                'client_ip' => get_client_ip(),
            ];

            $result = $api->goodCheck($params);

            return [
                'api_name' => '精准查涉案',
                'success' => $result['success'] ?? false,
                'data' => $result['data'] ?? null,
                'message' => $result['message'] ?? '查询失败',
                'query_time' => round(microtime(true) - $startTime, 2) . '秒',
                'params' => [
                    'orderno' => $params['orderno'],
                    'name' => $name,
                    'id_card' => $this->maskIdCard($idCard)
                ]
            ];

        } catch (\Exception $e) {
            return [
                'api_name' => '精准查涉案',
                'success' => false,
                'data' => null,
                'message' => '查询异常：' . $e->getMessage(),
                'query_time' => round(microtime(true) - $startTime, 2) . '秒',
                'error_code' => $e->getCode()
            ];
        }
    }

    /**
     * 聘查查API查询
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param string $queryType 查询类型
     * @return array 查询结果
     */
    private function queryPincc($name, $idCard, $queryType = 'all')
    {
        $results = [];
        $startTime = microtime(true);

        try {
            // 根据查询类型决定查询哪些接口
            $queries = $this->getPinccQueries($queryType);

            foreach ($queries as $key => $queryName) {
                $queryStartTime = microtime(true);

                try {
                    switch ($key) {
                        case 'marriage':
                            $result = ApifoxFactory::quickMarriageQuery($name, $idCard);
                            break;
                        case 'education':
                            $result = ApifoxFactory::quickEducationQueryV6($name, $idCard);
                            break;
                        case 'credit':
                            $result = ApifoxFactory::quickPersonCreditQuery($name, $idCard);
                            break;
                        default:
                            $result = ['success' => false, 'message' => '不支持的查询类型'];
                    }

                    $results[$key] = [
                        'api_name' => $queryName,
                        'success' => $result['success'] ?? false,
                        'data' => $result['data'] ?? null,
                        'message' => $result['message'] ?? '查询失败',
                        'query_time' => round(microtime(true) - $queryStartTime, 2) . '秒'
                    ];

                } catch (\Exception $e) {
                    $results[$key] = [
                        'api_name' => $queryName,
                        'success' => false,
                        'data' => null,
                        'message' => '查询异常：' . $e->getMessage(),
                        'query_time' => round(microtime(true) - $queryStartTime, 2) . '秒',
                        'error_code' => $e->getCode()
                    ];
                }
            }

            return [
                'total_apis' => count($queries),
                'total_time' => round(microtime(true) - $startTime, 2) . '秒',
                'results' => $results
            ];

        } catch (\Exception $e) {
            return [
                'total_apis' => 0,
                'total_time' => round(microtime(true) - $startTime, 2) . '秒',
                'error' => '聘查查API查询失败：' . $e->getMessage(),
                'results' => []
            ];
        }
    }

    /**
     * 根据前端选择构建查询配置
     * @param array $selectedItems 选择的单项
     * @param string $selectedPackage 选择的套餐
     * @return array 查询配置
     */
    private function buildQueryConfig($selectedItems, $selectedPackage)
    {
        // 定义所有可用的API映射
        $apiMapping = [
            // 单项服务映射
            'qiyu_prediction' => ['type' => 'goodcheck', 'name' => '奇遇预测', 'price' => 6.80],
            'vehicle_info' => ['type' => 'pincc_vehicle', 'name' => '各下车辆', 'price' => 15.80],
            'credit_check' => ['type' => 'pincc_credit', 'name' => '信贷逾期', 'price' => 4.80],
            'employment_record' => ['type' => 'pincc_employment', 'name' => '人企任职记录', 'price' => 12.80],
            'education_check' => ['type' => 'pincc_education', 'name' => '学历查询', 'price' => 5.80],
            'marriage_status' => ['type' => 'pincc_marriage', 'name' => '婚姻状况', 'price' => 15.80],
            'risk_list' => ['type' => 'goodcheck', 'name' => '风险清单', 'price' => 15.80],

            // 基础查询报告服务的子项
            'civil_cases' => ['type' => 'goodcheck_civil', 'name' => '民事案件', 'price' => 0],
            'criminal_cases' => ['type' => 'goodcheck_criminal', 'name' => '刑事案件', 'price' => 0],
            'administrative_cases' => ['type' => 'goodcheck_admin', 'name' => '行政案件', 'price' => 0],
            'dishonest_cases' => ['type' => 'pincc_dishonest', 'name' => '非诚案件', 'price' => 0],
            'execution_cases' => ['type' => 'goodcheck_execution', 'name' => '执行案件', 'price' => 0],
            'judgment_cases' => ['type' => 'goodcheck_judgment', 'name' => '强制清算与破产条件', 'price' => 0],
            'management_cases' => ['type' => 'pincc_management', 'name' => '营销案件', 'price' => 0],
            'verification_cases' => ['type' => 'pincc_verification', 'name' => '验证案件', 'price' => 0]
        ];

        // 定义套餐配置
        $packageMapping = [
            'basic_report' => [
                'name' => '基础查询报告服务',
                'price' => 35.8,
                'items' => ['civil_cases', 'criminal_cases', 'administrative_cases', 'dishonest_cases',
                           'execution_cases', 'judgment_cases', 'management_cases', 'verification_cases']
            ]
        ];

        $config = [
            'type' => 'custom', // custom, package
            'apis' => [],
            'total_price' => 0,
            'selected_items' => $selectedItems,
            'selected_package' => $selectedPackage
        ];

        // 如果选择了套餐
        if (!empty($selectedPackage) && isset($packageMapping[$selectedPackage])) {
            $package = $packageMapping[$selectedPackage];
            $config['type'] = 'package';
            $config['package_name'] = $package['name'];
            $config['total_price'] = $package['price'];

            // 添加套餐包含的所有API
            foreach ($package['items'] as $item) {
                if (isset($apiMapping[$item])) {
                    $config['apis'][$item] = $apiMapping[$item];
                }
            }
        }
        // 如果选择了单项
        elseif (!empty($selectedItems)) {
            $config['type'] = 'custom';

            foreach ($selectedItems as $item) {
                if (isset($apiMapping[$item])) {
                    $config['apis'][$item] = $apiMapping[$item];
                    $config['total_price'] += $apiMapping[$item]['price'];
                }
            }
        }
        // 如果什么都没选，默认基础查询
        else {
            $config['type'] = 'default';
            $config['apis'] = [
                'qiyu_prediction' => $apiMapping['qiyu_prediction'],
                'credit_check' => $apiMapping['credit_check'],
                'marriage_status' => $apiMapping['marriage_status']
            ];
            $config['total_price'] = 6.80 + 4.80 + 15.80; // 27.4
        }

        return $config;
    }

    /**
     * 根据配置进行同步查询
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $site 站点配置
     * @param array $queryConfig 查询配置
     * @return array 查询结果
     */
    private function syncQueryByConfig($name, $idCard, $site, $queryConfig)
    {
        $results = [];
        $startTime = microtime(true);

        foreach ($queryConfig['apis'] as $key => $apiConfig) {
            $queryStartTime = microtime(true);

            try {
                $result = $this->executeApiByType($apiConfig['type'], $name, $idCard, $site);

                $results[$key] = [
                    'api_name' => $apiConfig['name'],
                    'api_type' => $apiConfig['type'],
                    'success' => $result['success'] ?? false,
                    'data' => $result['data'] ?? null,
                    'message' => $result['message'] ?? '查询完成',
                    'query_time' => round(microtime(true) - $queryStartTime, 2) . '秒',
                    'price' => $apiConfig['price']
                ];

            } catch (\Exception $e) {
                $results[$key] = [
                    'api_name' => $apiConfig['name'],
                    'api_type' => $apiConfig['type'],
                    'success' => false,
                    'data' => null,
                    'message' => '查询异常：' . $e->getMessage(),
                    'query_time' => round(microtime(true) - $queryStartTime, 2) . '秒',
                    'price' => $apiConfig['price'],
                    'error_code' => $e->getCode()
                ];
            }
        }

        return [
            'query_type' => $queryConfig['type'],
            'total_apis' => count($queryConfig['apis']),
            'total_price' => $queryConfig['total_price'],
            'total_time' => round(microtime(true) - $startTime, 2) . '秒',
            'results' => $results
        ];
    }

    /**
     * 根据API类型执行具体查询
     * @param string $apiType API类型
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $site 站点配置
     * @return array 查询结果
     */
    private function executeApiByType($apiType, $name, $idCard, $site)
    {
        switch ($apiType) {
            case 'goodcheck':
            case 'goodcheck_civil':
            case 'goodcheck_criminal':
            case 'goodcheck_admin':
            case 'goodcheck_execution':
            case 'goodcheck_judgment':
                // 精准查涉案API（包含所有案件类型）
                $api = new GoodCheckApi($site['jc_appid'], $site['jc_secret']);
                $params = [
                    'orderno' => 'ORDER_' . time() . '_' . mt_rand(1000, 9999),
                    'name' => $name,
                    'code' => $idCard,
                    'querytype' => '1',
                    'detail' => '1',
                    'client_ip' => get_client_ip(),
                ];
                return $api->goodCheck($params);

            case 'pincc_marriage':
                // 婚姻状况查询
                return ApifoxFactory::quickMarriageQuery($name, $idCard);

            case 'pincc_education':
                // 学历信息查询
                return ApifoxFactory::quickEducationQueryV6($name, $idCard);

            case 'pincc_credit':
            case 'pincc_dishonest':
                // 个人失信查询
                return ApifoxFactory::quickPersonCreditQuery($name, $idCard);

            case 'pincc_vehicle':
                // 车辆信息查询（如果有对应接口）
                return ApifoxFactory::quickVehicleQuery($name, $idCard);

            case 'pincc_employment':
                // 任职记录查询（如果有对应接口）
                return ApifoxFactory::quickEmploymentQuery($name, $idCard);

            case 'pincc_management':
                // 营销案件查询（如果有对应接口）
                return ApifoxFactory::quickManagementQuery($name, $idCard);

            case 'pincc_verification':
                // 验证案件查询（如果有对应接口）
                return ApifoxFactory::quickVerificationQuery($name, $idCard);

            default:
                throw new \Exception("不支持的API类型: {$apiType}");
        }
    }

    /**
     * 根据配置进行并行查询
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $site 站点配置
     * @param array $queryConfig 查询配置
     * @return array 查询结果
     */
    private function parallelQueryByConfig($name, $idCard, $site, $queryConfig)
    {
        $startTime = microtime(true);
        $results = [];

        // 构建所有查询的URL和参数
        $queries = $this->buildParallelQueriesByConfig($name, $idCard, $site, $queryConfig);

        // 初始化curl multi handle
        $multiHandle = curl_multi_init();
        $curlHandles = [];

        // 为每个查询创建curl handle
        foreach ($queries as $key => $query) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $query['url']);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $query['data']);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $query['headers']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $curlHandles[$key] = $ch;
            curl_multi_add_handle($multiHandle, $ch);
        }

        // 执行并行请求
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
            curl_multi_select($multiHandle);
        } while ($running > 0);

        // 收集结果
        foreach ($curlHandles as $key => $ch) {
            $response = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            $queryInfo = $queries[$key];

            if ($error) {
                $results[$key] = [
                    'api_name' => $queryInfo['name'],
                    'api_type' => $queryInfo['type'],
                    'success' => false,
                    'data' => null,
                    'message' => 'CURL错误：' . $error,
                    'query_time' => '并行执行',
                    'price' => $queryInfo['price']
                ];
            } elseif ($httpCode !== 200) {
                $results[$key] = [
                    'api_name' => $queryInfo['name'],
                    'api_type' => $queryInfo['type'],
                    'success' => false,
                    'data' => null,
                    'message' => 'HTTP错误：' . $httpCode,
                    'query_time' => '并行执行',
                    'price' => $queryInfo['price']
                ];
            } else {
                $data = json_decode($response, true);
                $results[$key] = [
                    'api_name' => $queryInfo['name'],
                    'api_type' => $queryInfo['type'],
                    'success' => isset($data['success']) ? $data['success'] : ($httpCode === 200),
                    'data' => $data,
                    'message' => $data['message'] ?? '查询完成',
                    'query_time' => '并行执行',
                    'price' => $queryInfo['price']
                ];
            }

            curl_multi_remove_handle($multiHandle, $ch);
            curl_close($ch);
        }

        curl_multi_close($multiHandle);

        return [
            'query_type' => $queryConfig['type'],
            'total_apis' => count($queryConfig['apis']),
            'total_price' => $queryConfig['total_price'],
            'total_time' => round(microtime(true) - $startTime, 2) . '秒',
            'results' => $results
        ];
    }

    /**
     * 根据配置构建并行查询
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $site 站点配置
     * @param array $queryConfig 查询配置
     * @return array 查询配置
     */
    private function buildParallelQueriesByConfig($name, $idCard, $site, $queryConfig)
    {
        $queries = [];

        foreach ($queryConfig['apis'] as $key => $apiConfig) {
            $queries[$key] = $this->buildSingleApiQuery($apiConfig, $name, $idCard, $site);
        }

        return $queries;
    }

    /**
     * 构建单个API查询配置
     * @param array $apiConfig API配置
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $site 站点配置
     * @return array 查询配置
     */
    private function buildSingleApiQuery($apiConfig, $name, $idCard, $site)
    {
        $baseQuery = [
            'name' => $apiConfig['name'],
            'type' => $apiConfig['type'],
            'price' => $apiConfig['price']
        ];

        switch ($apiConfig['type']) {
            case 'goodcheck':
            case 'goodcheck_civil':
            case 'goodcheck_criminal':
            case 'goodcheck_admin':
            case 'goodcheck_execution':
            case 'goodcheck_judgment':
                return array_merge($baseQuery, [
                    'url' => 'https://api.example.com/goodcheck', // 实际API地址
                    'headers' => [
                        'Content-Type: application/json',
                        'X-Auth-Key: ' . $site['jc_appid'],
                    ],
                    'data' => json_encode([
                        'orderno' => 'ORDER_' . time() . '_' . mt_rand(1000, 9999),
                        'name' => $name,
                        'code' => $idCard,
                        'querytype' => '1',
                        'detail' => '1',
                        'client_ip' => get_client_ip()
                    ])
                ]);

            case 'pincc_marriage':
                return array_merge($baseQuery, [
                    'url' => 'https://api.edazi.com/api/marriage',
                    'headers' => [
                        'Content-Type: application/json',
                        'secret-key: your_secret_key',
                        'app-id: your_app_id'
                    ],
                    'data' => json_encode([
                        'name' => $name,
                        'card_no' => $idCard
                    ])
                ]);

            case 'pincc_education':
                return array_merge($baseQuery, [
                    'url' => 'https://api.edazi.com/api/education',
                    'headers' => [
                        'Content-Type: application/json',
                        'secret-key: your_secret_key',
                        'app-id: your_app_id'
                    ],
                    'data' => json_encode([
                        'name' => $name,
                        'card_no' => $idCard
                    ])
                ]);

            case 'pincc_credit':
            case 'pincc_dishonest':
                return array_merge($baseQuery, [
                    'url' => 'https://api.edazi.com/api/credit',
                    'headers' => [
                        'Content-Type: application/json',
                        'secret-key: your_secret_key',
                        'app-id: your_app_id'
                    ],
                    'data' => json_encode([
                        'name' => $name,
                        'card_no' => $idCard
                    ])
                ]);

            default:
                return array_merge($baseQuery, [
                    'url' => 'https://api.example.com/default',
                    'headers' => ['Content-Type: application/json'],
                    'data' => json_encode(['name' => $name, 'id_card' => $idCard])
                ]);
        }
    }

    /**
     * 根据配置进行队列查询（推荐用于批量处理）
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $site 站点配置
     * @param array $queryConfig 查询配置
     * @return array 队列任务信息
     */
    private function queueQueryByConfig($name, $idCard, $site, $queryConfig)
    {
        $taskId = 'multi_query_' . uniqid();
        $callbackUrl = $this->request->param('callback_url', '');

        try {
            // 根据配置构建查询任务列表
            $tasks = [];

            foreach ($queryConfig['apis'] as $key => $apiConfig) {
                $tasks[] = [
                    'task_id' => $taskId . '_' . $key,
                    'type' => $apiConfig['type'],
                    'api_name' => $apiConfig['name'],
                    'price' => $apiConfig['price'],
                    'params' => [
                        'name' => $name,
                        'id_card' => $idCard,
                        'api_type' => $apiConfig['type'],
                        'site_config' => $site
                    ]
                ];
            }

            // 将任务添加到队列
            $queuedTasks = [];
            foreach ($tasks as $task) {
                $jobId = Queue::push('app\\job\\MultiApiQuery', [
                    'main_task_id' => $taskId,
                    'task_data' => $task,
                    'callback_url' => $callbackUrl,
                    'created_at' => date('Y-m-d H:i:s')
                ], 'multi_api_query');

                $queuedTasks[] = [
                    'task_id' => $task['task_id'],
                    'job_id' => $jobId,
                    'api_name' => $task['api_name'],
                    'status' => 'queued'
                ];
            }

            // 缓存任务信息
            $taskInfo = [
                'main_task_id' => $taskId,
                'total_tasks' => count($tasks),
                'queued_tasks' => $queuedTasks,
                'query_info' => [
                    'name' => $name,
                    'id_card' => $this->maskIdCard($idCard),
                    'query_config' => $queryConfig,
                    'created_at' => date('Y-m-d H:i:s')
                ],
                'callback_url' => $callbackUrl,
                'status' => 'processing'
            ];

            Cache::set("queue_task_{$taskId}", $taskInfo, 3600); // 缓存1小时

            return [
                'success' => true,
                'message' => '查询任务已提交到队列',
                'task_id' => $taskId,
                'total_tasks' => count($tasks),
                'estimated_time' => '预计' . (count($tasks) * 2) . '-' . (count($tasks) * 5) . '分钟完成',
                'check_url' => url('index/checkQueueStatus', ['task_id' => $taskId]),
                'tasks' => $queuedTasks
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '队列任务提交失败：' . $e->getMessage(),
                'error_code' => $e->getCode()
            ];
        }
    }

    /**
     * 检查队列任务状态
     */
    public function checkQueueStatus()
    {
        $taskId = $this->request->param('task_id');

        if (empty($taskId)) {
            return json(['success' => false, 'message' => '任务ID不能为空']);
        }

        try {
            // 从缓存获取任务信息
            $taskInfo = Cache::get("queue_task_{$taskId}");
            if (!$taskInfo) {
                return json(['success' => false, 'message' => '任务不存在或已过期']);
            }

            // 检查各个子任务的完成状态
            $completedTasks = [];
            $failedTasks = [];
            $processingTasks = [];

            foreach ($taskInfo['queued_tasks'] as $task) {
                $resultKey = "queue_result_{$task['task_id']}";
                $result = Cache::get($resultKey);

                if ($result) {
                    if ($result['success']) {
                        $completedTasks[] = $result;
                    } else {
                        $failedTasks[] = $result;
                    }
                } else {
                    $processingTasks[] = $task;
                }
            }

            $totalTasks = count($taskInfo['queued_tasks']);
            $completedCount = count($completedTasks);
            $failedCount = count($failedTasks);
            $processingCount = count($processingTasks);

            // 判断整体状态
            $overallStatus = 'processing';
            if ($processingCount == 0) {
                $overallStatus = ($failedCount == 0) ? 'completed' : 'partial_completed';
            }

            $response = [
                'success' => true,
                'task_id' => $taskId,
                'status' => $overallStatus,
                'progress' => [
                    'total' => $totalTasks,
                    'completed' => $completedCount,
                    'failed' => $failedCount,
                    'processing' => $processingCount,
                    'percentage' => round(($completedCount + $failedCount) / $totalTasks * 100, 2)
                ],
                'query_info' => $taskInfo['query_info']
            ];

            // 如果全部完成，返回结果
            if ($overallStatus !== 'processing') {
                $response['results'] = [
                    'completed' => $completedTasks,
                    'failed' => $failedTasks
                ];

                // 生成摘要
                if (!empty($completedTasks)) {
                    $response['summary'] = $this->generateQueueSummary($completedTasks);
                }
            }

            return json($response);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '状态检查失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 并行查询（同时执行多个API）
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $site 站点配置
     * @param string $queryType 查询类型
     * @return array 查询结果
     */
    private function parallelQuery($name, $idCard, $site, $queryType)
    {
        $startTime = microtime(true);
        $results = [];

        // 构建所有查询的URL和参数
        $queries = $this->buildParallelQueries($name, $idCard, $site, $queryType);

        // 初始化curl multi handle
        $multiHandle = curl_multi_init();
        $curlHandles = [];

        // 为每个查询创建curl handle
        foreach ($queries as $key => $query) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $query['url']);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $query['data']);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $query['headers']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $curlHandles[$key] = $ch;
            curl_multi_add_handle($multiHandle, $ch);
        }

        // 执行并行请求
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
            curl_multi_select($multiHandle);
        } while ($running > 0);

        // 收集结果
        foreach ($curlHandles as $key => $ch) {
            $response = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            $queryInfo = $queries[$key];

            if ($error) {
                $results[$key] = [
                    'api_name' => $queryInfo['name'],
                    'success' => false,
                    'data' => null,
                    'message' => 'CURL错误：' . $error,
                    'query_time' => '0秒'
                ];
            } elseif ($httpCode !== 200) {
                $results[$key] = [
                    'api_name' => $queryInfo['name'],
                    'success' => false,
                    'data' => null,
                    'message' => 'HTTP错误：' . $httpCode,
                    'query_time' => '0秒'
                ];
            } else {
                $data = json_decode($response, true);
                $results[$key] = [
                    'api_name' => $queryInfo['name'],
                    'success' => isset($data['success']) ? $data['success'] : ($httpCode === 200),
                    'data' => $data,
                    'message' => $data['message'] ?? '查询完成',
                    'query_time' => '并行执行'
                ];
            }

            curl_multi_remove_handle($multiHandle, $ch);
            curl_close($ch);
        }

        curl_multi_close($multiHandle);

        // 重新组织结果格式，与同步查询保持一致
        $organizedResults = [];

        // 精准查涉案结果
        if (isset($results['goodcheck'])) {
            $organizedResults['goodcheck'] = $results['goodcheck'];
        }

        // 聘查查结果
        $pinccResults = [];
        foreach (['marriage', 'education', 'credit'] as $type) {
            if (isset($results[$type])) {
                $pinccResults[$type] = $results[$type];
            }
        }

        if (!empty($pinccResults)) {
            $organizedResults['pincc'] = [
                'total_apis' => count($pinccResults),
                'total_time' => round(microtime(true) - $startTime, 2) . '秒',
                'results' => $pinccResults
            ];
        }

        return $organizedResults;
    }

    /**
     * 构建并行查询配置
     * @param string $name 姓名
     * @param string $idCard 身份证号
     * @param array $site 站点配置
     * @param string $queryType 查询类型
     * @return array 查询配置
     */
    private function buildParallelQueries($name, $idCard, $site, $queryType)
    {
        $queries = [];

        // 精准查涉案API
        $queries['goodcheck'] = [
            'name' => '精准查涉案',
            'url' => 'https://api.example.com/goodcheck', // 实际API地址
            'headers' => [
                'Content-Type: application/json',
                'X-Auth-Key: ' . $site['jc_appid'],
                // 这里需要根据实际API添加签名等
            ],
            'data' => json_encode([
                'orderno' => 'ORDER_' . time() . '_' . mt_rand(1000, 9999),
                'name' => $name,
                'code' => $idCard,
                'querytype' => '1',
                'detail' => '1',
                'client_ip' => get_client_ip()
            ])
        ];

        // 聘查查API查询
        $pinccQueries = $this->getPinccQueries($queryType);
        foreach ($pinccQueries as $key => $apiName) {
            $queries[$key] = [
                'name' => $apiName,
                'url' => 'https://api.edazi.com/api/' . $this->getPinccEndpoint($key),
                'headers' => [
                    'Content-Type: application/json',
                    'secret-key: your_secret_key', // 从配置获取
                    'app-id: your_app_id'          // 从配置获取
                ],
                'data' => json_encode([
                    'name' => $name,
                    'card_no' => $idCard
                ])
            ];
        }

        return $queries;
    }

    /**
     * 获取聘查查API端点
     * @param string $type 查询类型
     * @return string API端点
     */
    private function getPinccEndpoint($type)
    {
        $endpoints = [
            'marriage' => 'marriage',
            'education' => 'education',
            'credit' => 'credit'
        ];

        return $endpoints[$type] ?? 'unknown';
    }

    /**
     * 获取聘查查查询配置
     * @param string $queryType 查询类型
     * @return array 查询配置
     */
    private function getPinccQueries($queryType)
    {
        $allQueries = [
            'marriage' => '婚姻状况查询',
            'education' => '学历信息查询',
            'credit' => '个人失信查询'
        ];

        switch ($queryType) {
            case 'basic':
                return [
                    'marriage' => $allQueries['marriage'],
                    'credit' => $allQueries['credit']
                ];
            case 'detail':
                return $allQueries;
            case 'all':
            default:
                return $allQueries;
        }
    }

    /**
     * 生成查询结果摘要
     * @param array $results 查询结果
     * @return array 摘要信息
     */
    private function generateSummary($results)
    {
        $summary = [
            'total_apis' => 0,
            'success_count' => 0,
            'failed_count' => 0,
            'risk_level' => 'unknown',
            'risk_factors' => [],
            'recommendations' => []
        ];

        // 统计精准查涉案结果
        if (isset($results['goodcheck'])) {
            $summary['total_apis']++;
            if ($results['goodcheck']['success']) {
                $summary['success_count']++;
                $summary = $this->analyzeGoodCheckRisk($results['goodcheck'], $summary);
            } else {
                $summary['failed_count']++;
            }
        }

        // 统计聘查查结果
        if (isset($results['pincc']['results'])) {
            foreach ($results['pincc']['results'] as $result) {
                $summary['total_apis']++;
                if ($result['success']) {
                    $summary['success_count']++;
                    $summary = $this->analyzePinccRisk($result, $summary);
                } else {
                    $summary['failed_count']++;
                }
            }
        }

        // 计算总体风险等级
        $summary['risk_level'] = $this->calculateRiskLevel($summary['risk_factors']);
        $summary['recommendations'] = $this->generateRecommendations($summary['risk_level'], $summary['risk_factors']);

        return $summary;
    }

    /**
     * 分析精准查涉案风险
     * @param array $result 查询结果
     * @param array $summary 摘要信息
     * @return array 更新后的摘要
     */
    private function analyzeGoodCheckRisk($result, $summary)
    {
        if (isset($result['data']['data']['checkdata']['stats'])) {
            $stats = $result['data']['data']['checkdata']['stats'];

            // 分析案件数量
            $totalCases = $stats['count_total'] ?? 0;
            $unfinishedCases = $stats['count_wei_total'] ?? 0;
            $totalMoney = $stats['money_total'] ?? 0;

            if ($totalCases > 0) {
                $summary['risk_factors'][] = "涉案记录：{$totalCases}件案件";

                if ($unfinishedCases > 0) {
                    $summary['risk_factors'][] = "未结案件：{$unfinishedCases}件";
                }

                if ($totalMoney > 0) {
                    $summary['risk_factors'][] = "涉案金额：{$totalMoney}万元";
                }
            }
        }

        return $summary;
    }

    /**
     * 分析聘查查风险
     * @param array $result 查询结果
     * @param array $summary 摘要信息
     * @return array 更新后的摘要
     */
    private function analyzePinccRisk($result, $summary)
    {
        if (isset($result['data']['data'])) {
            $data = $result['data']['data'];

            // 根据不同API类型分析
            if (strpos($result['api_name'], '失信') !== false) {
                // 失信查询分析
                if (isset($data['resultCode']) && $data['resultCode'] == '1') {
                    $summary['risk_factors'][] = "存在失信记录";
                }
            } elseif (strpos($result['api_name'], '婚姻') !== false) {
                // 婚姻状况分析
                if (isset($data['resultCode'])) {
                    $summary['risk_factors'][] = "婚姻状况：" . $this->getMarriageStatus($data['resultCode']);
                }
            }
        }

        return $summary;
    }

    /**
     * 计算风险等级
     * @param array $riskFactors 风险因素
     * @return string 风险等级
     */
    private function calculateRiskLevel($riskFactors)
    {
        $riskCount = count($riskFactors);

        if ($riskCount == 0) {
            return 'low';
        } elseif ($riskCount <= 2) {
            return 'medium';
        } else {
            return 'high';
        }
    }

    /**
     * 生成建议
     * @param string $riskLevel 风险等级
     * @param array $riskFactors 风险因素
     * @return array 建议列表
     */
    private function generateRecommendations($riskLevel, $riskFactors)
    {
        $recommendations = [];

        switch ($riskLevel) {
            case 'low':
                $recommendations[] = "风险较低，可正常合作";
                break;
            case 'medium':
                $recommendations[] = "存在一定风险，建议加强监控";
                $recommendations[] = "可考虑适当的风控措施";
                break;
            case 'high':
                $recommendations[] = "风险较高，建议谨慎合作";
                $recommendations[] = "需要详细的风险评估";
                $recommendations[] = "建议要求额外担保措施";
                break;
        }

        return $recommendations;
    }

    /**
     * 获取婚姻状况描述
     * @param string $code 状态码
     * @return string 状态描述
     */
    private function getMarriageStatus($code)
    {
        $statusMap = [
            '1' => '已婚',
            '2' => '未婚',
            '3' => '离异',
            '4' => '丧偶',
            '0' => '未知'
        ];

        return $statusMap[$code] ?? '未知';
    }

    /**
     * 身份证号脱敏
     * @param string $idCard 身份证号
     * @return string 脱敏后的身份证号
     */
    private function maskIdCard($idCard)
    {
        if (strlen($idCard) < 8) {
            return $idCard;
        }

        return substr($idCard, 0, 6) . '****' . substr($idCard, -4);
    }

    /**
     * 生成队列查询结果摘要
     * @param array $completedTasks 已完成的任务
     * @return array 摘要信息
     */
    private function generateQueueSummary($completedTasks)
    {
        $summary = [
            'total_apis' => count($completedTasks),
            'success_count' => 0,
            'failed_count' => 0,
            'risk_level' => 'unknown',
            'risk_factors' => [],
            'recommendations' => []
        ];

        foreach ($completedTasks as $task) {
            if ($task['success']) {
                $summary['success_count']++;

                // 根据API类型分析风险
                switch ($task['type']) {
                    case 'goodcheck':
                        $summary = $this->analyzeGoodCheckRisk($task, $summary);
                        break;
                    case 'pincc_marriage':
                    case 'pincc_education':
                    case 'pincc_credit':
                        $summary = $this->analyzePinccRisk($task, $summary);
                        break;
                }
            } else {
                $summary['failed_count']++;
            }
        }

        // 计算总体风险等级
        $summary['risk_level'] = $this->calculateRiskLevel($summary['risk_factors']);
        $summary['recommendations'] = $this->generateRecommendations($summary['risk_level'], $summary['risk_factors']);

        return $summary;
    }

    /**
     * 批量队列查询（用于批量处理多个用户）
     */
    public function batchQueueQuery()
    {
        $users = $this->request->param('users', []);
        $queryType = $this->request->param('query_type', 'all');
        $callbackUrl = $this->request->param('callback_url', '');

        if (empty($users)) {
            return json(['success' => false, 'message' => '用户列表不能为空']);
        }

        $site = config('site');
        $batchId = 'batch_' . uniqid();
        $taskIds = [];

        try {
            foreach ($users as $index => $user) {
                if (empty($user['name']) || empty($user['id_card'])) {
                    continue;
                }

                $taskId = $batchId . '_user_' . $index;

                // 为每个用户创建队列任务
                Queue::push('app\\job\\MultiApiQuery', [
                    'main_task_id' => $taskId,
                    'task_data' => [
                        'task_id' => $taskId,
                        'type' => 'batch_user',
                        'api_name' => '批量用户查询',
                        'params' => [
                            'name' => $user['name'],
                            'id_card' => $user['id_card'],
                            'query_type' => $queryType,
                            'site_config' => $site
                        ]
                    ],
                    'callback_url' => $callbackUrl,
                    'created_at' => date('Y-m-d H:i:s')
                ], 'batch_query');

                $taskIds[] = $taskId;
            }

            // 缓存批量任务信息
            $batchInfo = [
                'batch_id' => $batchId,
                'total_users' => count($taskIds),
                'task_ids' => $taskIds,
                'query_type' => $queryType,
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 'processing'
            ];

            Cache::set("batch_task_{$batchId}", $batchInfo, 7200); // 缓存2小时

            return json([
                'success' => true,
                'message' => '批量查询任务已提交',
                'batch_id' => $batchId,
                'total_users' => count($taskIds),
                'estimated_time' => '预计' . (count($taskIds) * 3) . '-' . (count($taskIds) * 8) . '分钟完成',
                'check_url' => url('index/checkBatchStatus', ['batch_id' => $batchId])
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '批量任务提交失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查批量任务状态
     */
    public function checkBatchStatus()
    {
        $batchId = $this->request->param('batch_id');

        if (empty($batchId)) {
            return json(['success' => false, 'message' => '批量任务ID不能为空']);
        }

        try {
            $batchInfo = Cache::get("batch_task_{$batchId}");
            if (!$batchInfo) {
                return json(['success' => false, 'message' => '批量任务不存在或已过期']);
            }

            $completedCount = 0;
            $failedCount = 0;
            $results = [];

            foreach ($batchInfo['task_ids'] as $taskId) {
                $result = Cache::get("queue_result_{$taskId}");
                if ($result) {
                    $results[] = $result;
                    if ($result['success']) {
                        $completedCount++;
                    } else {
                        $failedCount++;
                    }
                }
            }

            $totalUsers = $batchInfo['total_users'];
            $processingCount = $totalUsers - $completedCount - $failedCount;

            return json([
                'success' => true,
                'batch_id' => $batchId,
                'status' => $processingCount > 0 ? 'processing' : 'completed',
                'progress' => [
                    'total' => $totalUsers,
                    'completed' => $completedCount,
                    'failed' => $failedCount,
                    'processing' => $processingCount,
                    'percentage' => round(($completedCount + $failedCount) / $totalUsers * 100, 2)
                ],
                'results' => $results
            ]);

        } catch (\Exception $e) {
            return json([
                'success' => false,
                'message' => '批量状态检查失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 查询结果页面
     */
    public function queryResult()
    {
        return $this->view->fetch('query_result');
    }

    /**
     * 队列状态页面
     */
    public function queueStatus()
    {
        return $this->view->fetch('queue_status');
    }
}