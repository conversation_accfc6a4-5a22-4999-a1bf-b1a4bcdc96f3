<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Banner;

class Index extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        // 获取轮播图数据
        $bannerList = Banner::where('status', 'normal')
            ->order('weigh', 'desc')
            ->select();
        
        // 判断是否有轮播图数据
        $hasBanner = count($bannerList);
        
        $this->view->assign([
            'bannerList' => $bannerList,
            'hasBanner' => $hasBanner
        ]);
        return $this->view->fetch();
    }
}