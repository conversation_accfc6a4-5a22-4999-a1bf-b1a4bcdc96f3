<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Banner;
use app\common\model\Package;

class Index extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        // 获取轮播图数据
        $bannerList = Banner::where('status', 'normal')
            ->order('weigh', 'desc')
            ->select();

        // 判断是否有轮播图数据
        $hasBanner = count($bannerList);

        // 获取套餐数据
        $singleList = Package::getSingleList();
        $packageList = Package::getPackageList();

        $this->view->assign([
            'bannerList' => $bannerList,
            'hasBanner' => $hasBanner,
            'singleList' => $singleList,
            'packageList' => $packageList
        ]);
        return $this->view->fetch();
    }
}