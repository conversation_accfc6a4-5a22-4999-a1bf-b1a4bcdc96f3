<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Banner;
use app\common\model\Package;
use app\common\model\ServiceItem;
use api\GoodCheckApi;

class Index extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        // 获取轮播图数据
        $bannerList = Banner::where('status', 'normal')
            ->order('weigh', 'desc')
            ->select();

        // 判断是否有轮播图数据
        $hasBanner = count($bannerList);

        // 获取固定的服务项目（单项）
        $singleList = ServiceItem::getAvailableItems();

        // 获取套餐数据
        $packageList = Package::getPackageList();

        $this->view->assign([
            'bannerList' => $bannerList,
            'hasBanner' => $hasBanner,
            'singleList' => $singleList,
            'packageList' => $packageList
        ]);
        return $this->view->fetch();
    }

    /**
     * 查询信息
     */
    public function query()
    {
        $api = new GoodCheckApi('3499653071082nfxtc8', 'c249d94a83fd118a49d20c855b14f71f');
        $params = [
            'orderno' => 'ORDER_' . time(),
            'name' => ['刘建汉'],  // 支持多个姓名
            'code' => '51310119881207121X',
            'detail' => '1',           // 1为详细版，0为简版
            'client_ip' => '***********',
            'interfacecoding' => 'sf003',
        ];

        $result = $api->goodCheck($params);
        dump($params);
        dump($result);
    }
}