<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\ServiceItem as ServiceItemModel;

/**
 * 服务项目管理（固定项目，只能编辑价格）
 */
class ServiceItem extends Backend
{
    /**
     * ServiceItem模型对象
     * @var \app\common\model\ServiceItem
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new ServiceItemModel;

        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','name','description','current_price','original_price','weigh','status','createtime','updatetime']);
                $row->visible(['status_text']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 编辑（只允许编辑价格和状态）
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 只允许编辑特定字段
                $allowFields = ['current_price', 'original_price', 'status', 'weigh'];
                $updateData = [];
                foreach ($allowFields as $field) {
                    if (isset($params[$field])) {
                        $updateData[$field] = $params[$field];
                    }
                }
                
                $result = false;
                \think\Db::startTrans();
                try {
                    $result = $row->allowField($allowFields)->save($updateData);
                    \think\Db::commit();
                } catch (\think\exception\PDOException $e) {
                    \think\Db::rollback();
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    \think\Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 禁用添加功能
     */
    public function add()
    {
        $this->error('服务项目为固定项目，不允许添加');
    }

    /**
     * 禁用删除功能
     */
    public function del()
    {
        $this->error('服务项目为固定项目，不允许删除');
    }
}
