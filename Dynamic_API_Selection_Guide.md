# 动态API选择查询指南

## 🎯 概述

根据前端用户的选择动态决定查询哪些API，支持：
- **单项服务选择**：用户可以选择具体的查询项目
- **套餐服务选择**：用户可以选择预定义的套餐
- **智能默认查询**：如果用户什么都没选，提供基础查询

## 📋 支持的查询项目

### 🎯 单项服务
| 项目ID | 服务名称 | API类型 | 价格 | 说明 |
|--------|----------|---------|------|------|
| `qiyu_prediction` | 奇遇预测 | goodcheck | ¥6.80 | 生辰八字预测 |
| `vehicle_info` | 各下车辆 | pincc_vehicle | ¥15.80 | 企业风险评估，经营状况 |
| `credit_check` | 信贷逾期 | pincc_credit | ¥4.80 | 学历验证，学校信息 |
| `employment_record` | 人企任职记录 | pincc_employment | ¥12.80 | 法人代表信息，企业任职信息 |
| `education_check` | 学历查询 | pincc_education | ¥5.80 | 身份验证，手机号验证 |
| `marriage_status` | 婚姻状况 | pincc_marriage | ¥15.80 | 企业风险评估，经营状况 |
| `risk_list` | 风险清单 | goodcheck | ¥15.80 | 企业风险评估，经营状况 |

### 📦 套餐服务
| 套餐ID | 套餐名称 | 价格 | 包含项目 |
|--------|----------|------|----------|
| `basic_report` | 基础查询报告服务 | ¥35.8 | 民事案件、刑事案件、行政案件、非诚案件、执行案件、强制清算与破产条件、营销案件、验证案件 |

## 🚀 使用方法

### 1. 选择单项服务查询
```
GET /index/query?name=张三&id_card=******************&selected_items[]=qiyu_prediction&selected_items[]=credit_check&selected_items[]=marriage_status
```

**参数说明：**
- `selected_items[]`：选择的单项服务ID数组
- 可以选择多个单项服务
- 总价格为所有选择项目的价格之和

### 2. 选择套餐服务查询
```
GET /index/query?name=张三&id_card=******************&selected_package=basic_report
```

**参数说明：**
- `selected_package`：选择的套餐ID
- 套餐价格固定，包含多个查询项目
- 选择套餐时会忽略单项选择

### 3. 默认查询（什么都没选）
```
GET /index/query?name=张三&id_card=******************
```

**默认包含：**
- 奇遇预测（¥6.80）
- 信贷逾期（¥4.80）
- 婚姻状况（¥15.80）
- 总价：¥27.40

### 4. 指定查询策略
```
GET /index/query?name=张三&id_card=******************&selected_items[]=qiyu_prediction&strategy=parallel
```

**策略选项：**
- `auto`：自动选择（默认）
- `sync`：同步查询
- `parallel`：并行查询
- `queue`：队列查询

## 📊 返回数据结构

### 成功响应
```json
{
    "success": true,
    "message": "联合查询完成",
    "query_info": {
        "name": "张三",
        "id_card": "420101****0001",
        "query_time": "2025-06-23 10:30:00",
        "total_time": "3.45秒",
        "selected_items": ["qiyu_prediction", "credit_check"],
        "selected_package": "",
        "query_config": {
            "type": "custom",
            "apis": {
                "qiyu_prediction": {
                    "type": "goodcheck",
                    "name": "奇遇预测",
                    "price": 6.80
                },
                "credit_check": {
                    "type": "pincc_credit",
                    "name": "信贷逾期",
                    "price": 4.80
                }
            },
            "total_price": 11.60
        }
    },
    "results": {
        "query_type": "custom",
        "total_apis": 2,
        "total_price": 11.60,
        "total_time": "3.45秒",
        "results": {
            "qiyu_prediction": {
                "api_name": "奇遇预测",
                "api_type": "goodcheck",
                "success": true,
                "data": { /* 查询结果数据 */ },
                "message": "查询成功",
                "query_time": "2.1秒",
                "price": 6.80
            },
            "credit_check": {
                "api_name": "信贷逾期",
                "api_type": "pincc_credit",
                "success": true,
                "data": { /* 查询结果数据 */ },
                "message": "查询成功",
                "query_time": "1.35秒",
                "price": 4.80
            }
        }
    },
    "summary": {
        "total_apis": 2,
        "success_count": 2,
        "failed_count": 0,
        "risk_level": "low",
        "risk_factors": [],
        "recommendations": ["风险较低，可正常合作"]
    }
}
```

## 🎨 前端集成示例

### HTML表单
```html
<!-- 套餐选择 -->
<div class="package-section">
    <input type="radio" name="package" value="basic_report" id="package_basic">
    <label for="package_basic">基础查询报告服务 - ¥35.8</label>
</div>

<!-- 单项选择 -->
<div class="items-section">
    <input type="checkbox" name="items[]" value="qiyu_prediction" id="item_qiyu">
    <label for="item_qiyu">奇遇预测 - ¥6.80</label>
    
    <input type="checkbox" name="items[]" value="credit_check" id="item_credit">
    <label for="item_credit">信贷逾期 - ¥4.80</label>
    
    <input type="checkbox" name="items[]" value="marriage_status" id="item_marriage">
    <label for="item_marriage">婚姻状况 - ¥15.80</label>
</div>
```

### JavaScript处理
```javascript
function submitQuery() {
    const formData = new FormData();
    formData.append('name', document.getElementById('name').value);
    formData.append('id_card', document.getElementById('idCard').value);
    
    // 获取选择的套餐
    const selectedPackage = document.querySelector('input[name="package"]:checked');
    if (selectedPackage) {
        formData.append('selected_package', selectedPackage.value);
    }
    
    // 获取选择的单项
    const selectedItems = document.querySelectorAll('input[name="items[]"]:checked');
    selectedItems.forEach(item => {
        formData.append('selected_items[]', item.value);
    });
    
    // 发送请求
    fetch('/index/query', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayResults(data);
        } else {
            showError(data.message);
        }
    });
}
```

## 🔧 后端实现逻辑

### 查询配置构建
```php
private function buildQueryConfig($selectedItems, $selectedPackage)
{
    // 1. 如果选择了套餐
    if (!empty($selectedPackage)) {
        return $this->buildPackageConfig($selectedPackage);
    }
    
    // 2. 如果选择了单项
    if (!empty($selectedItems)) {
        return $this->buildCustomConfig($selectedItems);
    }
    
    // 3. 默认配置
    return $this->buildDefaultConfig();
}
```

### API执行分发
```php
private function executeApiByType($apiType, $name, $idCard, $site)
{
    switch ($apiType) {
        case 'goodcheck':
            return $this->callGoodCheckApi($name, $idCard, $site);
        case 'pincc_marriage':
            return $this->callPinccMarriageApi($name, $idCard);
        case 'pincc_education':
            return $this->callPinccEducationApi($name, $idCard);
        // ... 其他API类型
    }
}
```

## 💡 最佳实践

### 1. 前端用户体验
- **互斥选择**：套餐和单项不能同时选择
- **价格实时计算**：选择变化时立即更新总价
- **智能推荐**：根据用户行为推荐合适的套餐

### 2. 后端处理优化
- **配置缓存**：API映射配置可以缓存
- **批量验证**：一次性验证所有选择的有效性
- **错误隔离**：单个API失败不影响其他API

### 3. 业务逻辑
- **价格策略**：套餐价格优于单项组合
- **权限控制**：不同用户可访问不同的API
- **使用统计**：记录用户的选择偏好

## ⚠️ 注意事项

### 1. 参数验证
- 验证选择的项目ID是否有效
- 检查套餐和单项的互斥性
- 确保至少有一个查询项目

### 2. 价格计算
- 套餐价格固定，不受包含项目影响
- 单项价格累加计算
- 考虑折扣和优惠策略

### 3. API限制
- 某些API可能有调用频率限制
- 部分API可能需要特殊权限
- 考虑API的可用性和稳定性

这种动态选择机制让用户可以根据实际需求灵活选择查询内容，既满足了个性化需求，又提供了经济实惠的套餐选择！🚀
