<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateBannerTable extends Migrator
{
    public function up()
    {
        $table = $this->table('banner');
        $table->addColumn('title', 'string', ['limit' => 100, 'default' => '', 'comment' => '标题'])
            ->addColumn('image', 'string', ['limit' => 255, 'default' => '', 'comment' => '图片'])
            ->addColumn('url', 'string', ['limit' => 255, 'default' => '', 'comment' => '链接'])
            ->addColumn('weigh', 'integer', ['default' => 0, 'comment' => '权重'])
            ->addColumn('status', 'enum', ['values' => ['normal', 'hidden'], 'default' => 'normal', 'comment' => '状态'])
            ->addColumn('createtime', 'integer', ['signed' => false, 'default' => 0, 'comment' => '创建时间'])
            ->addColumn('updatetime', 'integer', ['signed' => false, 'default' => 0, 'comment' => '更新时间'])
            ->addIndex(['weigh'], ['name' => 'weigh'])
            ->addIndex(['status'], ['name' => 'status'])
            ->create();
    }

    public function down()
    {
        $this->dropTable('banner');
    }
}