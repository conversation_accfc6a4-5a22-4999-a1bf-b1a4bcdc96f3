<?php
/**
 * 测试JSON格式API请求
 */

require_once 'GoodCheckApi.php';
require_once '../apifox/PinccApi.php';
require_once '../apifox/config/ApifoxConfig.php';

use api\GoodCheckApi;
use apifox\PinccApi;
use apifox\config\ApifoxConfig;

echo "=== API JSON请求格式测试 ===\n\n";

// 1. 测试精准查涉案API（JSON格式）
echo "1. 测试精准查涉案API（JSON格式）\n";
echo "-----------------------------------\n";

try {
    // 注意：这里需要替换为实际的appId和secret
    $goodCheckApi = new GoodCheckApi('your_app_id', 'your_secret');
    
    echo "✅ 精准查涉案API实例创建成功\n";
    echo "请求格式：JSON (Content-Type: application/json)\n";
    
    // 测试个人查询（这里只是演示，不会真正发送请求）
    echo "个人查询参数示例：\n";
    $personParams = [
        'name' => ['张三'],
        'code' => '420101199001010001',
        'querytype' => '0',
        'nonce_str' => 'test123456789',
        'datanumber' => '1',
        'detail' => '1',
        'serviceCode' => 'ALL',
        'classtype' => 'sf',
        'checktype' => '1'
    ];
    echo "JSON格式: " . json_encode($personParams, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "❌ 精准查涉案API测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. 测试聘查查API（JSON格式）
echo "2. 测试聘查查API（JSON格式）\n";
echo "-----------------------------------\n";

try {
    // 检查配置
    $validation = ApifoxConfig::validateConfig();
    if ($validation['valid']) {
        $pinccApi = new PinccApi();
        echo "✅ 聘查查API实例创建成功\n";
        echo "请求格式：JSON (Content-Type: application/json)\n";
        
        // 显示配置信息
        $config = $pinccApi->getConfig();
        echo "配置信息：\n";
        foreach ($config as $key => $value) {
            echo "  {$key}: {$value}\n";
        }
        
        // 测试参数示例
        echo "婚姻查询参数示例：\n";
        $marriageParams = [
            'name' => '李四',
            'card_no' => '******************'
        ];
        echo "JSON格式: " . json_encode($marriageParams, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        
    } else {
        echo "❌ 聘查查API配置未完成：\n";
        foreach ($validation['errors'] as $error) {
            echo "  - {$error}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 聘查查API测试失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. JSON格式的优势说明
echo "3. JSON格式的优势\n";
echo "-----------------------------------\n";
echo "✅ 更好的数据结构支持：可以直接传递数组和对象\n";
echo "✅ 更清晰的数据格式：易于阅读和调试\n";
echo "✅ 更好的Unicode支持：支持中文等特殊字符\n";
echo "✅ 更标准的API格式：符合现代API设计规范\n";
echo "✅ 更好的类型保持：数字、布尔值等类型不会丢失\n";

echo "\n";

// 4. 请求头对比
echo "4. 请求头对比\n";
echo "-----------------------------------\n";
echo "修改前（表单格式）：\n";
echo "  Content-Type: application/x-www-form-urlencoded\n";
echo "  请求体: name=张三&code=420101199001010001&querytype=0\n";
echo "\n";
echo "修改后（JSON格式）：\n";
echo "  Content-Type: application/json\n";
echo "  请求体: {\"name\":[\"张三\"],\"code\":\"420101199001010001\",\"querytype\":\"0\"}\n";

echo "\n";

// 5. 注意事项
echo "5. 注意事项\n";
echo "-----------------------------------\n";
echo "⚠️  确保API服务端支持JSON格式请求\n";
echo "⚠️  如果服务端不支持JSON，可能需要回退到表单格式\n";
echo "⚠️  测试时请确认API返回结果是否正常\n";
echo "⚠️  某些老旧的API可能只支持表单格式\n";

echo "\n=== 测试完成 ===\n";

/**
 * 如果需要回退到表单格式，可以使用以下代码：
 * 
 * // 表单格式请求头
 * $headers = [
 *     'Content-Type: application/x-www-form-urlencoded',
 *     // 其他头信息...
 * ];
 * 
 * // 表单格式请求体
 * curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
 */
