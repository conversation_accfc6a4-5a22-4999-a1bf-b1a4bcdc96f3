# 队列查询实战示例

## 🎯 场景一：夜间批量风控检查

### 业务需求
每天晚上对所有新注册用户进行背景调查，生成风控报告。

### 实现方案
```php
// 1. 获取今日新注册用户
$newUsers = Db::table('users')
    ->where('create_time', '>=', date('Y-m-d 00:00:00'))
    ->where('status', 'active')
    ->field('id,name,id_card')
    ->select();

// 2. 提交批量队列任务
$batchData = [
    'users' => $newUsers,
    'query_type' => 'all',
    'callback_url' => 'https://your-domain.com/api/risk-report-callback'
];

$response = $this->post('/index/batchQueueQuery', $batchData);

// 3. 记录批量任务ID
if ($response['success']) {
    Db::table('batch_tasks')->insert([
        'batch_id' => $response['batch_id'],
        'task_type' => 'daily_risk_check',
        'total_users' => count($newUsers),
        'status' => 'processing',
        'created_at' => date('Y-m-d H:i:s')
    ]);
}
```

### 回调处理
```php
// 风控报告回调处理
public function riskReportCallback()
{
    $data = $this->request->getContent();
    $result = json_decode($data, true);
    
    if ($result['status'] === 'completed') {
        // 生成风控报告
        $this->generateRiskReport($result['batch_id'], $result['results']);
        
        // 发送邮件通知
        $this->sendRiskReportEmail($result['batch_id']);
    }
}
```

## 🎯 场景二：实时用户注册风控

### 业务需求
用户注册时立即进行背景调查，但不阻塞注册流程。

### 实现方案
```php
// 用户注册控制器
public function register()
{
    // 1. 正常注册流程
    $user = $this->createUser($userData);
    
    // 2. 异步提交风控查询
    $this->submitRiskCheck($user);
    
    // 3. 立即返回注册成功
    return json(['success' => true, 'message' => '注册成功']);
}

private function submitRiskCheck($user)
{
    // 提交队列查询
    $response = $this->get('/index/query', [
        'strategy' => 'queue',
        'name' => $user['name'],
        'id_card' => $user['id_card'],
        'query_type' => 'basic',
        'callback_url' => url('user/riskCheckCallback', ['user_id' => $user['id']])
    ]);
    
    // 记录任务ID
    if ($response['success']) {
        Db::table('user_risk_tasks')->insert([
            'user_id' => $user['id'],
            'task_id' => $response['task_id'],
            'status' => 'processing',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
```

## 🎯 场景三：定时合作伙伴背调

### 业务需求
每周对重要合作伙伴进行背景调查更新。

### 定时任务设置
```php
// 在crontab中设置
// 0 2 * * 1 cd /path/to/project && php think partner:weekly-check

// 定时任务命令
class PartnerWeeklyCheck extends Command
{
    public function execute()
    {
        // 获取需要检查的合作伙伴
        $partners = Db::table('partners')
            ->where('status', 'active')
            ->where('risk_level', 'in', ['medium', 'high'])
            ->select();
        
        $users = [];
        foreach ($partners as $partner) {
            $users[] = [
                'name' => $partner['contact_name'],
                'id_card' => $partner['contact_id_card']
            ];
        }
        
        // 提交批量查询
        $this->submitBatchQuery($users, 'weekly_partner_check');
    }
}
```

## 🎯 场景四：API限流处理

### 业务需求
某些API有严格的频率限制，需要控制调用速度。

### 实现方案
```php
// 自定义队列任务，添加延迟
public function submitLimitedQuery($userData)
{
    $delay = $this->calculateDelay(); // 计算延迟时间
    
    Queue::later($delay, 'app\\job\\MultiApiQuery', [
        'main_task_id' => $taskId,
        'task_data' => $taskData,
        'rate_limit' => true
    ], 'limited_api_query');
}

// 在队列任务中处理限流
class MultiApiQuery
{
    public function fire(Job $job, $data)
    {
        if (isset($data['rate_limit']) && $data['rate_limit']) {
            // 检查API调用频率
            $this->checkRateLimit($data['task_data']['type']);
        }
        
        // 执行查询...
    }
    
    private function checkRateLimit($apiType)
    {
        $key = "rate_limit_{$apiType}";
        $count = Cache::get($key, 0);
        
        if ($count >= 10) { // 每分钟最多10次
            sleep(60); // 等待1分钟
            Cache::set($key, 0, 60);
        } else {
            Cache::set($key, $count + 1, 60);
        }
    }
}
```

## 🎯 场景五：失败任务重试机制

### 业务需求
网络不稳定时，确保重要查询最终能够成功。

### 实现方案
```php
// 增强的重试逻辑
class MultiApiQuery
{
    public function fire(Job $job, $data)
    {
        try {
            $result = $this->executeApiQuery($data['task_data']);
            // 成功处理...
            
        } catch (\Exception $e) {
            $attempts = $job->attempts();
            
            // 根据错误类型决定重试策略
            if ($this->shouldRetry($e, $attempts)) {
                $delay = $this->getRetryDelay($attempts);
                $job->release($delay);
                
                Log::info("任务重试: {$data['task_data']['task_id']}, 尝试次数: {$attempts}, 延迟: {$delay}秒");
            } else {
                // 最终失败，记录详细错误信息
                $this->handleFinalFailure($data, $e);
                $job->delete();
            }
        }
    }
    
    private function shouldRetry($exception, $attempts)
    {
        // 网络错误：重试3次
        if (strpos($exception->getMessage(), 'curl') !== false) {
            return $attempts < 3;
        }
        
        // API限流：重试5次
        if (strpos($exception->getMessage(), 'rate limit') !== false) {
            return $attempts < 5;
        }
        
        // 其他错误：重试1次
        return $attempts < 1;
    }
    
    private function getRetryDelay($attempts)
    {
        // 指数退避：1分钟、2分钟、4分钟
        return pow(2, $attempts - 1) * 60;
    }
}
```

## 🎯 场景六：队列监控告警

### 业务需求
队列积压或处理异常时及时通知运维人员。

### 监控脚本
```php
// 队列监控脚本
class QueueMonitor
{
    public function checkQueueHealth()
    {
        $redis = new \Redis();
        $redis->connect('127.0.0.1', 6379);
        
        $alerts = [];
        
        // 检查队列长度
        $queues = ['multi_api_query', 'batch_query', 'default'];
        foreach ($queues as $queue) {
            $length = $redis->lLen("queues:{$queue}");
            
            if ($length > 100) {
                $alerts[] = "队列 {$queue} 积压严重：{$length} 个任务";
            }
        }
        
        // 检查失败率
        $failureRate = $this->calculateFailureRate();
        if ($failureRate > 0.1) { // 失败率超过10%
            $alerts[] = "任务失败率过高：" . ($failureRate * 100) . "%";
        }
        
        // 发送告警
        if (!empty($alerts)) {
            $this->sendAlert($alerts);
        }
    }
    
    private function sendAlert($alerts)
    {
        $message = "队列告警：\n" . implode("\n", $alerts);
        
        // 发送邮件/短信/钉钉通知
        $this->sendEmail('<EMAIL>', '队列告警', $message);
        $this->sendDingTalk($message);
    }
}
```

## 🎯 场景七：队列性能优化

### 业务需求
提高队列处理速度，减少用户等待时间。

### 优化策略
```php
// 1. 批量处理优化
class BatchOptimizedQuery
{
    public function fire(Job $job, $data)
    {
        $batchSize = 5; // 每次处理5个用户
        $users = $data['users'];
        
        // 分批处理
        $batches = array_chunk($users, $batchSize);
        
        foreach ($batches as $batch) {
            $this->processBatch($batch);
            
            // 避免API限流
            usleep(500000); // 0.5秒延迟
        }
    }
    
    private function processBatch($users)
    {
        // 并行处理批次内的用户
        $multiHandle = curl_multi_init();
        $curlHandles = [];
        
        foreach ($users as $user) {
            $ch = $this->createCurlHandle($user);
            $curlHandles[] = $ch;
            curl_multi_add_handle($multiHandle, $ch);
        }
        
        // 执行并行请求
        $running = null;
        do {
            curl_multi_exec($multiHandle, $running);
            curl_multi_select($multiHandle);
        } while ($running > 0);
        
        // 处理结果...
    }
}

// 2. 智能队列分发
class SmartQueueDispatcher
{
    public function dispatch($taskData)
    {
        // 根据API类型选择队列
        $queue = $this->selectQueue($taskData['type']);
        
        // 根据优先级调整延迟
        $delay = $this->calculatePriority($taskData);
        
        Queue::later($delay, 'app\\job\\MultiApiQuery', $taskData, $queue);
    }
    
    private function selectQueue($apiType)
    {
        // 高频API使用专用队列
        if (in_array($apiType, ['goodcheck', 'pincc_credit'])) {
            return 'high_frequency_api';
        }
        
        return 'default';
    }
}
```

## 📊 性能监控仪表板

### 实时监控页面
```javascript
// 队列监控前端
class QueueDashboard {
    constructor() {
        this.updateInterval = 5000; // 5秒更新一次
        this.startMonitoring();
    }
    
    startMonitoring() {
        setInterval(() => {
            this.updateQueueStats();
            this.updateTaskProgress();
            this.updatePerformanceMetrics();
        }, this.updateInterval);
    }
    
    updateQueueStats() {
        fetch('/admin/queue/stats')
            .then(response => response.json())
            .then(data => {
                this.renderQueueChart(data.queues);
                this.updateAlerts(data.alerts);
            });
    }
    
    renderQueueChart(queues) {
        // 使用Chart.js渲染队列状态图表
        const ctx = document.getElementById('queueChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Object.keys(queues),
                datasets: [{
                    label: '队列长度',
                    data: Object.values(queues),
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            }
        });
    }
}

// 启动监控
new QueueDashboard();
```

这些实战示例展示了队列查询在不同业务场景中的应用，帮助你根据实际需求选择合适的实现方案！🚀
