# 队列查询使用指南

## 🎯 概述

队列查询是一种异步处理方式，特别适合以下场景：
- **批量用户背景调查**
- **夜间定时风控检查**
- **大量数据处理**
- **不需要实时结果的查询**

## 🚀 核心优势

### ✅ 稳定可靠
- **任务持久化**：任务存储在Redis队列中，服务重启不丢失
- **自动重试**：失败任务自动重试3次
- **错误隔离**：单个API失败不影响其他API

### ⚡ 高性能
- **异步处理**：不阻塞用户请求
- **并发执行**：多个worker同时处理任务
- **资源优化**：避免大量并发请求导致系统压力

### 📊 可监控
- **实时状态**：随时查询任务进度
- **详细日志**：完整的执行记录
- **回调通知**：任务完成自动通知

## 📋 使用方法

### 1. 单用户队列查询

#### 提交查询任务
```
GET /index/query?strategy=queue&name=张三&id_card=******************&query_type=all
```

#### 响应示例
```json
{
    "success": true,
    "message": "查询任务已提交到队列",
    "task_id": "multi_query_60f7b8c9d4e12",
    "total_tasks": 4,
    "estimated_time": "预计8-20分钟完成",
    "check_url": "/index/checkQueueStatus?task_id=multi_query_60f7b8c9d4e12",
    "tasks": [
        {
            "task_id": "multi_query_60f7b8c9d4e12_goodcheck",
            "api_name": "精准查涉案",
            "status": "queued"
        },
        {
            "task_id": "multi_query_60f7b8c9d4e12_marriage",
            "api_name": "婚姻状况查询",
            "status": "queued"
        }
    ]
}
```

### 2. 检查任务状态

#### 查询进度
```
GET /index/checkQueueStatus?task_id=multi_query_60f7b8c9d4e12
```

#### 进行中响应
```json
{
    "success": true,
    "task_id": "multi_query_60f7b8c9d4e12",
    "status": "processing",
    "progress": {
        "total": 4,
        "completed": 2,
        "failed": 0,
        "processing": 2,
        "percentage": 50.0
    },
    "query_info": {
        "name": "张三",
        "id_card": "420101****0001",
        "query_type": "all",
        "created_at": "2025-06-23 10:30:00"
    }
}
```

#### 完成响应
```json
{
    "success": true,
    "task_id": "multi_query_60f7b8c9d4e12",
    "status": "completed",
    "progress": {
        "total": 4,
        "completed": 4,
        "failed": 0,
        "processing": 0,
        "percentage": 100.0
    },
    "results": {
        "completed": [
            {
                "task_id": "multi_query_60f7b8c9d4e12_goodcheck",
                "api_name": "精准查涉案",
                "success": true,
                "data": { /* 查询结果 */ },
                "query_time": "2.3秒",
                "completed_at": "2025-06-23 10:32:15"
            }
        ],
        "failed": []
    },
    "summary": {
        "risk_level": "medium",
        "risk_factors": ["涉案记录：2件案件"],
        "recommendations": ["建议加强监控"]
    }
}
```

### 3. 批量用户查询

#### 提交批量任务
```
POST /index/batchQueueQuery
Content-Type: application/json

{
    "users": [
        {"name": "张三", "id_card": "******************"},
        {"name": "李四", "id_card": "******************"},
        {"name": "王五", "id_card": "******************"}
    ],
    "query_type": "all",
    "callback_url": "https://your-domain.com/callback"
}
```

#### 批量响应
```json
{
    "success": true,
    "message": "批量查询任务已提交",
    "batch_id": "batch_60f7b8c9d4e12",
    "total_users": 3,
    "estimated_time": "预计9-24分钟完成",
    "check_url": "/index/checkBatchStatus?batch_id=batch_60f7b8c9d4e12"
}
```

### 4. 检查批量状态

#### 查询批量进度
```
GET /index/checkBatchStatus?batch_id=batch_60f7b8c9d4e12
```

#### 批量状态响应
```json
{
    "success": true,
    "batch_id": "batch_60f7b8c9d4e12",
    "status": "processing",
    "progress": {
        "total": 3,
        "completed": 2,
        "failed": 0,
        "processing": 1,
        "percentage": 66.67
    },
    "results": [
        {
            "task_id": "batch_60f7b8c9d4e12_user_0",
            "success": true,
            "data": { /* 张三的查询结果 */ }
        },
        {
            "task_id": "batch_60f7b8c9d4e12_user_1",
            "success": true,
            "data": { /* 李四的查询结果 */ }
        }
    ]
}
```

## 🔧 队列配置

### 1. Redis配置
在 `application/extra/queue.php` 中：
```php
return [
    'connector'  => 'Redis',
    'expire'     => 0,
    'default'    => 'default',
    'host'       => '127.0.0.1',
    'port'       => 6379,
    'password'   => '',
    'select'     => 0,
    'timeout'    => 0,
    'persistent' => false,
];
```

### 2. 启动队列Worker
```bash
# 启动单个worker
php think queue:work

# 启动多个worker（推荐）
php think queue:work --queue=multi_api_query &
php think queue:work --queue=batch_query &
php think queue:work --queue=default &
```

### 3. 监控队列
```bash
# 查看队列状态
php think queue:status

# 重启队列
php think queue:restart

# 清空失败任务
php think queue:flush
```

## 📊 性能对比

### 队列查询 vs 实时查询
| 对比项 | 队列查询 | 实时查询 |
|--------|----------|----------|
| **响应时间** | 立即返回任务ID | 等待所有API完成 |
| **用户体验** | 无需等待 | 需要等待3-10秒 |
| **系统压力** | 低 | 高 |
| **并发能力** | 高 | 受限 |
| **可靠性** | 很高（可重试） | 中等 |
| **适用场景** | 批量处理 | 实时查询 |

### 处理能力
- **单worker**：每分钟处理10-20个用户
- **多worker**：每分钟处理50-100个用户
- **批量模式**：每小时处理1000+用户

## ⚠️ 注意事项

### 1. 队列管理
- **定期清理**：清理过期的缓存数据
- **监控告警**：监控队列长度和处理速度
- **容错处理**：处理Redis连接失败等异常

### 2. 资源控制
- **并发限制**：控制同时运行的worker数量
- **内存管理**：避免内存泄漏
- **API限流**：遵守各API的调用频率限制

### 3. 数据安全
- **敏感信息**：身份证号等敏感信息脱敏处理
- **结果缓存**：设置合理的缓存过期时间
- **访问控制**：限制队列状态查询的访问权限

## 🎨 前端集成

### JavaScript示例
```javascript
// 提交队列查询
function submitQueueQuery(userData) {
    $.ajax({
        url: '/index/query',
        data: {
            strategy: 'queue',
            name: userData.name,
            id_card: userData.id_card,
            query_type: 'all'
        },
        success: function(response) {
            if (response.success) {
                // 开始轮询检查状态
                pollTaskStatus(response.task_id);
            }
        }
    });
}

// 轮询检查任务状态
function pollTaskStatus(taskId) {
    const interval = setInterval(function() {
        $.ajax({
            url: '/index/checkQueueStatus',
            data: { task_id: taskId },
            success: function(response) {
                updateProgress(response.progress);
                
                if (response.status !== 'processing') {
                    clearInterval(interval);
                    displayResults(response.results);
                }
            }
        });
    }, 5000); // 每5秒检查一次
}
```

## 🎯 最佳实践

### 1. 使用场景选择
- **实时查询**：用户等待结果，1-3个API
- **队列查询**：批量处理，不需要立即结果
- **混合模式**：重要信息实时查询，补充信息队列查询

### 2. 性能优化
- **合理设置worker数量**：根据服务器性能调整
- **批量提交**：一次提交多个用户，减少网络开销
- **结果缓存**：避免重复查询相同用户

### 3. 监控运维
- **队列长度监控**：避免队列积压
- **失败率监控**：及时发现API问题
- **性能监控**：监控处理速度和响应时间

队列查询为大规模数据处理提供了稳定、高效的解决方案！🚀
