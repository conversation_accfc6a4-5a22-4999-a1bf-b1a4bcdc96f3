<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Banner as BannerModel;
use think\Config;

class Banner extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 轮播图首页
     */
    public function index()
    {
        $bannerList = $this->getBannerList();
        $this->view->assign('bannerList', $bannerList);
        return $this->view->fetch();
    }

    /**
     * 获取轮播图列表
     */
    public function getBanners()
    {
        $bannerList = $this->getBannerList();
        return json(['code' => 1, 'msg' => 'success', 'data' => $bannerList]);
    }

    /**
     * 获取轮播图列表
     */
    protected function getBannerList()
    {
        $bannerModel = new BannerModel;
        $bannerList = $bannerModel
            ->where('status', 'normal')
            ->order('weigh', 'desc')
            ->select();
        
        foreach ($bannerList as &$item) {
            $item['image'] = cdnurl($item['image'], true);
        }
        
        return $bannerList;
    }
}