define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'serviceitem/index' + location.search,
                    edit_url: 'serviceitem/edit',
                    del_url: '', // 禁用删除
                    multi_url: 'serviceitem/multi',
                    import_url: '', // 禁用导入
                    table: 'service_item',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'description', title: __('Description'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'current_price', title: __('Current_price'), operate: 'BETWEEN', addclass: 'text-right', formatter: function(value, row, index) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'original_price', title: __('Original_price'), operate: 'BETWEEN', addclass: 'text-right', formatter: function(value, row, index) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'weigh', title: __('Weigh'), operate: false, addclass: 'text-center'},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass: 'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass: 'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, 
                            buttons: [
                                {
                                    name: 'edit',
                                    title: __('Edit'),
                                    classname: 'btn btn-xs btn-success btn-editone',
                                    icon: 'fa fa-pencil',
                                    url: 'serviceitem/edit'
                                }
                            ],
                            formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 隐藏添加按钮
            $(".btn-add").hide();

            // 隐藏删除按钮
            $(".btn-del").hide();

            // 修改工具栏提示
            $(".toolbar").prepend('<div class="alert alert-info alert-dismissible"><button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button><i class="fa fa-info-circle"></i> 服务项目为系统固定项目，只能修改价格、权重和状态，不能添加或删除。</div>');

            // 绑定批量操作事件
            $(document).on("click", ".btn-multi", function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error(__('Please select at least one record'));
                    return false;
                }

                var params = $(this).data("params") || {};
                var url = "serviceitem/multi";

                Layer.confirm(__('Are you sure you want to perform this operation?'), function(index) {
                    var data = {ids: ids};
                    if (params) {
                        data.row = params;
                    }

                    Fast.api.ajax({
                        url: url,
                        data: data,
                        type: "POST"
                    }, function(data, ret) {
                        Layer.close(index);
                        $(".btn-refresh").trigger("click");
                        Toastr.success(__('Operation completed'));
                    });
                });
                return false;
            });
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
