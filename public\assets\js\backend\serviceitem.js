define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'serviceitem/index' + location.search,
                    edit_url: 'serviceitem/edit',
                    del_url: '', // 禁用删除
                    multi_url: 'serviceitem/multi',
                    import_url: '', // 禁用导入
                    table: 'service_item',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                sortOrder: 'desc',
                search: false,
                showSearch: false,
                showToggle: false,
                showColumns: false,
                showRefresh: true,
                columns: [
                    [
                        //{checkbox: true},
                        //{field: 'id', title: __('Id'), operate: false},
                        {field: 'name', title: __('Name'), operate: false, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'description', title: __('Description'), operate: false, table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'current_price', title: __('Current_price'), operate: false, addclass: 'text-right', formatter: function(value) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'original_price', title: __('Original_price'), operate: false, addclass: 'text-right', formatter: function(value) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'weigh', title: __('Weigh'), operate: false, addclass: 'text-center'},
                        {field: 'status', title: __('Status'), operate: false, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate: false, addclass: 'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate: false, addclass: 'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'edit',
                                    title: __('Edit'),
                                    classname: 'btn btn-xs btn-success btn-editone',
                                    icon: 'fa fa-pencil',
                                    url: 'serviceitem/edit'
                                }
                            ],
                            formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 隐藏添加按钮
            $(".btn-add").hide();

            // 隐藏删除按钮
            $(".btn-del").hide();

            // 绑定批量操作事件
            $(document).on("click", ".btn-multi", function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Toastr.error(__('Please select at least one record'));
                    return false;
                }

                var params = $(this).data("params") || {};
                var url = "serviceitem/multi";

                Layer.confirm(__('Are you sure you want to perform this operation?'), function(index) {
                    var data = {ids: ids};
                    if (params) {
                        data.row = params;
                    }

                    Fast.api.ajax({
                        url: url,
                        data: data,
                        type: "POST"
                    }, function() {
                        Layer.close(index);
                        $(".btn-refresh").trigger("click");
                        Toastr.success(__('Operation completed'));
                    });
                });
                return false;
            });
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
